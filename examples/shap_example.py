#!/usr/bin/env python3
"""
Example script demonstrating SHAP explainability for NEXCO traffic prediction.

This script shows how to:
1. Train a traffic prediction model
2. Set up SHAP explainers
3. Generate predictions with explanations
4. Create visualization plots
5. Analyze feature importance

Usage:
    python examples/shap_example.py
"""

import sys
import os
import numpy as np
from pathlib import Path

# Add the sequence module to the path
sys.path.append(str(Path(__file__).parent.parent))

from sequence import NEXCOTrafficSystem, Config
from sequence.explainer import TrafficSHAPExplainer, SHAPAnalysisReport


def main():
    """Main example function."""
    print("🔍 NEXCO Traffic Prediction - SHAP Explainability Example")
    print("=" * 60)
    
    # 1. Create configuration with SHAP enabled
    config = Config()
    config.shap.enable_shap = True
    config.shap.generate_plots = True
    config.shap.save_plots = True
    config.shap.plots_directory = "./example_shap_plots"
    
    # Use quick test config for faster execution
    config.data.months_limit = 1
    config.prediction.max_routes_training = 2
    config.training.epochs = 10
    
    print(f"📊 SHAP Configuration:")
    print(f"  • Explainer type: {config.shap.explainer_type}")
    print(f"  • Background samples: {config.shap.background_samples}")
    print(f"  • Generate plots: {config.shap.generate_plots}")
    print(f"  • Plots directory: {config.shap.plots_directory}")
    print()
    
    # 2. Initialize the system
    system = NEXCOTrafficSystem(config)
    
    # 3. Verify and load data
    print("📁 Loading data...")
    if not system.verify_data():
        print("❌ Data verification failed. Please run preprocessing first.")
        return 1
    
    df = system.load_data()
    print(f"✅ Loaded {len(df):,} traffic records")
    
    # 4. Train models
    print("\n🚀 Training models...")
    trained_models = system.train_models(df)
    
    if not trained_models:
        print("❌ No models were trained successfully")
        return 1
    
    print(f"✅ Trained {len(trained_models)} models")
    
    # 5. Set up SHAP explainers
    print("\n🔍 Setting up SHAP explainers...")
    shap_success = system.setup_shap_explainers()
    
    if not shap_success:
        print("❌ Failed to set up SHAP explainers")
        return 1
    
    print("✅ SHAP explainers ready")
    
    # 6. Demonstrate SHAP explanations
    print("\n📊 Generating SHAP explanations...")
    
    # Get a route to demonstrate
    available_routes = list(trained_models.keys())
    demo_route = available_routes[0]
    print(f"🛣️  Demonstrating with route: {demo_route}")
    
    # Get some sample data for prediction
    route_data = df[
        (df['ic_name_start'] + ' to ' + df['ic_name_end']) == demo_route
    ].copy()
    
    if len(route_data) < 200:
        print(f"⚠️ Limited data for {demo_route} ({len(route_data)} samples)")
    
    # Prepare sample input data
    from sequence.data_loader import TrafficDataLoader
    loader = TrafficDataLoader()
    
    try:
        sequences = loader.create_sequences(
            route_data, 
            input_steps=config.model.input_steps,
            output_steps=config.model.output_steps
        )
        
        if sequences is None:
            print("❌ Failed to create sequences")
            return 1
        
        X_dur, X_feat, y = sequences
        
        # Use the last sequence as our "recent data" for prediction
        recent_dur = X_dur[-1]  # Shape: (144, 1)
        recent_feat = X_feat[-1]  # Shape: (144, n_features)
        
        print(f"📈 Input data shape: Duration {recent_dur.shape}, Features {recent_feat.shape}")
        
        # 7. Make prediction with explanation
        print("\n🎯 Making prediction with SHAP explanation...")
        
        result = system.predict_with_explanation(
            demo_route, 
            recent_dur, 
            recent_feat,
            generate_plots=True,
            save_dir=config.shap.plots_directory
        )
        
        if result['prediction'] is None:
            print("❌ Prediction failed")
            return 1
        
        # 8. Display results
        print("\n📊 SHAP Analysis Results:")
        print(f"  • Prediction available: ✅")
        print(f"  • SHAP explanation: {'✅' if result['shap_available'] else '❌'}")
        
        if result['shap_available']:
            print(f"  • Feature importance calculated: ✅")
            print(f"  • SHAP values shape: {result['shap_values'].shape}")
            
            # Show top features
            if result['feature_importance']:
                print("\n🏆 Top 5 Most Important Features:")
                importance = result['feature_importance']
                top_features = sorted(importance.items(), 
                                    key=lambda x: abs(x[1]), reverse=True)[:5]
                
                for i, (feature, score) in enumerate(top_features, 1):
                    impact = "📈" if score > 0 else "📉"
                    print(f"  {i}. {feature}: {score:.4f} {impact}")
            
            # Check if plots were generated
            plots_dir = Path(config.shap.plots_directory)
            if plots_dir.exists():
                plot_files = list(plots_dir.glob("*.png"))
                print(f"\n📊 Generated {len(plot_files)} visualization plots:")
                for plot_file in plot_files:
                    print(f"  • {plot_file.name}")
        
        # 9. Feature importance summary across all routes
        print("\n🌐 Feature Importance Summary (All Routes):")
        importance_summary = system.predictor.get_feature_importance_summary()
        
        for route, summary in importance_summary.items():
            print(f"\n🛣️  {route}:")
            for i, (feature, score) in enumerate(summary['top_features'], 1):
                print(f"    {i}. {feature}: {score:.4f}")
        
        print("\n✅ SHAP example completed successfully!")
        print(f"📁 Check {config.shap.plots_directory} for visualization plots")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error during SHAP demonstration: {e}")
        import traceback
        traceback.print_exc()
        return 1


def demonstrate_advanced_shap():
    """Demonstrate advanced SHAP features."""
    print("\n🔬 Advanced SHAP Features Demo")
    print("-" * 40)
    
    # This would be called after basic setup
    print("Advanced features include:")
    print("  • Time-series specific explanations")
    print("  • Feature interaction analysis")
    print("  • Temporal importance patterns")
    print("  • Route comparison analysis")
    print("  • Custom visualization themes")
    
    # Note: Implementation would go here for advanced features


if __name__ == "__main__":
    try:
        exit_code = main()
        
        # Optionally run advanced demo
        if exit_code == 0:
            demonstrate_advanced_shap()
        
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        print("\n⛔ Example interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
