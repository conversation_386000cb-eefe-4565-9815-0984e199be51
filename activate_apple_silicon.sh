#!/bin/bash
# Activation script for NEXCO Apple Silicon environment

echo "🍎 Activating Apple Silicon optimized environment for NEXCO Traffic Analysis..."

# Activate the virtual environment
source nexco_apple_silicon_env/bin/activate

echo "✅ Environment activated!"
echo ""
echo "🚀 Available optimizations:"
echo "   • MLX Framework for Neural Engine/GPU acceleration"
echo "   • Polars for faster data processing"
echo "   • PyArrow for optimized I/O operations"
echo "   • NumPy with Apple Accelerate framework"
echo ""
echo "📚 To run the notebook:"
echo "   jupyter lab Nexco_Traffic_AppleSilicon.ipynb"
echo ""
echo "🔧 To deactivate:"
echo "   deactivate"