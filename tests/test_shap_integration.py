"""
Test suite for SHAP integration in NEXCO traffic prediction system.
"""

import pytest
import numpy as np
import tempfile
import shutil
from pathlib import Path
import sys

# Add sequence module to path
sys.path.append(str(Path(__file__).parent.parent))

from sequence.explainer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rap<PERSON>, TrafficSHAPExplainer, SHAPAnalysisReport
from sequence.models import MLXLSTMModel
from sklearn.preprocessing import MinMaxScaler


class TestMLXModelWrapper:
    """Test the MLX model wrapper for SHAP compatibility."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Create a simple MLX model
        self.model = MLXLSTMModel(
            duration_input_size=1,
            feature_input_size=8,
            hidden_size=32,
            output_size=12
        )
        
        # Create mock scalers
        self.scaler_duration = MinMaxScaler()
        self.scaler_features = MinMaxScaler()
        
        # Fit scalers with dummy data
        dummy_duration = np.random.randn(100, 1)
        dummy_features = np.random.randn(100, 8)
        
        self.scaler_duration.fit(dummy_duration)
        self.scaler_features.fit(dummy_features)
        
        # Create wrapper
        self.wrapper = MLXModelWrapper(
            model=self.model,
            scaler_duration=self.scaler_duration,
            scaler_features=self.scaler_features,
            input_steps=144
        )
    
    def test_wrapper_initialization(self):
        """Test wrapper initialization."""
        assert self.wrapper.model is not None
        assert self.wrapper.scaler_duration is not None
        assert self.wrapper.scaler_features is not None
        assert self.wrapper.input_steps == 144
    
    def test_wrapper_predict(self):
        """Test wrapper prediction function."""
        # Create test input (batch_size=2, seq_len*features=144*9)
        batch_size = 2
        total_features = 9  # 1 duration + 8 features
        X = np.random.randn(batch_size, 144 * total_features)
        
        # Make prediction
        predictions = self.wrapper.predict(X)
        
        # Check output shape
        assert predictions.shape == (batch_size, 12)
        assert isinstance(predictions, np.ndarray)


class TestTrafficSHAPExplainer:
    """Test the SHAP explainer for traffic prediction."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Create model and wrapper
        model = MLXLSTMModel(
            duration_input_size=1,
            feature_input_size=8,
            hidden_size=32,
            output_size=12
        )
        
        scaler_duration = MinMaxScaler()
        scaler_features = MinMaxScaler()
        
        # Fit scalers
        dummy_duration = np.random.randn(100, 1)
        dummy_features = np.random.randn(100, 8)
        scaler_duration.fit(dummy_duration)
        scaler_features.fit(dummy_features)
        
        wrapper = MLXModelWrapper(
            model=model,
            scaler_duration=scaler_duration,
            scaler_features=scaler_features,
            input_steps=144
        )
        
        # Create explainer
        self.explainer = TrafficSHAPExplainer(wrapper)
        
        # Create test data
        self.X_dur = np.random.randn(50, 144, 1)
        self.X_feat = np.random.randn(50, 144, 8)
    
    def test_explainer_initialization(self):
        """Test explainer initialization."""
        assert self.explainer.model_wrapper is not None
        assert len(self.explainer.feature_names) == 15  # 1 duration + 14 features
        assert self.explainer.explainer is None  # Not fitted yet
    
    def test_background_data_preparation(self):
        """Test background data preparation."""
        background = self.explainer.prepare_background_data(
            self.X_dur, self.X_feat, n_background=20
        )
        
        assert background.shape[0] == 20
        assert background.shape[1] == 144 * 9  # seq_len * (1 + 8 features)
        assert self.explainer.background_data is not None
    
    def test_explainer_fitting(self):
        """Test SHAP explainer fitting."""
        # Prepare background data
        background = self.explainer.prepare_background_data(
            self.X_dur, self.X_feat, n_background=10
        )
        
        # Fit explainer (use small sample for speed)
        self.explainer.fit_explainer(background, explainer_type='permutation')
        
        assert self.explainer.explainer is not None
    
    def test_feature_importance_calculation(self):
        """Test feature importance calculation."""
        # Create mock SHAP values
        batch_size = 5
        seq_len = 144
        n_features = 9
        
        mock_shap_values = np.random.randn(batch_size, seq_len * n_features)
        
        # Calculate importance
        importance = self.explainer.get_feature_importance(
            mock_shap_values, aggregate_time=True
        )
        
        assert isinstance(importance, dict)
        assert len(importance) == len(self.explainer.feature_names)
        
        # Check all values are numeric
        for feature, score in importance.items():
            assert isinstance(score, (int, float, np.number))


class TestSHAPAnalysisReport:
    """Test the SHAP analysis report generator."""
    
    def setup_method(self):
        """Set up test fixtures."""
        # Create minimal explainer for testing
        model = MLXLSTMModel(duration_input_size=1, feature_input_size=8, 
                           hidden_size=16, output_size=12)
        
        scaler_duration = MinMaxScaler()
        scaler_features = MinMaxScaler()
        
        # Fit scalers
        dummy_duration = np.random.randn(50, 1)
        dummy_features = np.random.randn(50, 8)
        scaler_duration.fit(dummy_duration)
        scaler_features.fit(dummy_features)
        
        wrapper = MLXModelWrapper(model, scaler_duration, scaler_features, 144)
        self.explainer = TrafficSHAPExplainer(wrapper)
        
        # Create report generator
        self.report_generator = SHAPAnalysisReport(self.explainer)
        
        # Test data
        self.X_dur = np.random.randn(10, 144, 1)
        self.X_feat = np.random.randn(10, 144, 8)
    
    def test_report_initialization(self):
        """Test report generator initialization."""
        assert self.report_generator.explainer is not None
    
    def test_report_generation_structure(self):
        """Test report generation returns correct structure."""
        # Mock SHAP values for testing
        self.explainer.shap_values = np.random.randn(10, 144 * 9)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            report = self.report_generator.generate_report(
                self.X_dur, self.X_feat, 
                route_name="Test Route",
                save_dir=temp_dir
            )
            
            # Check report structure
            assert 'route_name' in report
            assert 'n_samples' in report
            assert 'n_features' in report
            assert 'feature_importance' in report
            assert 'top_features' in report
            assert 'summary' in report
            
            # Check summary structure
            summary = report['summary']
            assert 'most_important_feature' in summary
            assert 'most_important_score' in summary
            assert 'total_absolute_importance' in summary


class TestIntegrationScenarios:
    """Test integration scenarios and edge cases."""
    
    def test_feature_names_consistency(self):
        """Test that feature names are consistent across components."""
        # Create explainer
        model = MLXLSTMModel(duration_input_size=1, feature_input_size=8)
        wrapper = MLXModelWrapper(model, MinMaxScaler(), MinMaxScaler(), 144)
        explainer = TrafficSHAPExplainer(wrapper)
        
        # Check default feature names
        expected_features = [
            'duration_seconds',
            'hour', 'day_of_week', 'is_weekend', 'is_rush_hour', 'is_holiday',
            'has_precipitation', 'max_precip',
            'temp_start', 'temp_end', 'precip_start', 'precip_end',
            'wind_speed_start', 'wind_speed_end',
            'speed_profile_numeric'
        ]
        
        assert explainer.feature_names == expected_features
    
    def test_input_shape_validation(self):
        """Test input shape validation and error handling."""
        model = MLXLSTMModel(duration_input_size=1, feature_input_size=8)
        wrapper = MLXModelWrapper(model, MinMaxScaler(), MinMaxScaler(), 144)
        
        # Test with wrong input shape
        wrong_shape_input = np.random.randn(2, 100)  # Wrong shape
        
        with pytest.raises((ValueError, IndexError)):
            wrapper.predict(wrong_shape_input)


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
