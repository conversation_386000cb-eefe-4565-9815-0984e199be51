# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Setup

### Environment Activation
Use the Apple Silicon optimized environment:
```bash
source ./activate_apple_silicon.sh
# OR manually:
source nexco_apple_silicon_env/bin/activate
```

### Dependencies
Main dependencies are in `sequence/requirements.txt`. The system uses:
- MLX for Apple Silicon hardware acceleration
- Polars for high-performance data processing
- MLflow for experiment tracking
- PyArrow for optimized I/O operations

## System Architecture

This is a **NEXCO Traffic Prediction System** with Apple Silicon (MLX) optimization for Japanese highway traffic forecasting.

### Core Components

1. **Data Pipeline (Two-Stage Processing)**:
   - `preprocessors/process_traffic.py` - Converts raw CSV files to processed Parquet files with ML feature extraction from base data
   - `sequence/data_loader.py` - Simple loader for preprocessed Parquet files

2. **ML Pipeline**:
   - `sequence/models.py` - Enhanced dual-stream LSTM model (MLXLSTMModel)
   - `sequence/trainer.py` - MLX-optimized training with experiment tracking
   - `sequence/predictor.py` - Traffic prediction and confidence estimation

3. **Configuration**:
   - `sequence/config.py` - Centralized configuration with presets (default, quick, production)

### Data Structure
```
data/
├── raw/           # Original CSV files (git-ignored)
│   ├── traffic/   # Traffic data by month/day (YYYYMM/DD/*.csv)
│   ├── events/    # Road event data by month/day (YYYYMM/DD/*.csv)
│   └── weather/   # Weather data by IC location (individual parquet files)
│       └── <ic_name>/ # Individual IC directories (あきる野ＩＣ, etc.)
│           ├── 2024.parquet # Full year hourly weather data
│           └── 2025.parquet # Current year up to yesterday
├── processed/     # Processed Parquet files
│   ├── traffic/   # ML-ready traffic datasets with enhanced features
│   └── events/    # Processed road events
└── constants/     # Static reference data
    ├── ic_locations.json  # IC coordinates (128 locations)
    ├── highway_data.json  # Highway metadata
    ├── ic_dict.json       # IC dictionary
    └── jct_info.json      # Junction info
```

### Raw Data Column Schemas

**Traffic CSV Files** (`data/raw/traffic/YYYYMM/DD/*.csv`) - 32 columns:
- `0` through `23` (24 columns) - Hourly traffic duration values
- `weekday` - Day of week
- `is_holiday` - Holiday flag
- `time_band` - Time period classification
- `speed_profile` - Highway speed classification
- `connecting_roads_from` - Starting route connections
- `connecting_roads_to` - Ending route connections  
- `precip_from` - Precipitation at start location
- `precip_to` - Precipitation at end location

**Road Events CSV Files** (`data/raw/events/YYYYMM/DD/*.csv`) - ~35 columns:
Event data with Japanese headers including region, event type, timestamps, IC names, directions, lane restrictions, and event details.

**Weather Parquet Files** (`data/raw/weather/<ic_name>/YYYY.parquet`) - 10 columns:
1. `ic_name` (string) - IC/JCT location identifier
2. `timestamp` (datetime) - Hourly timestamp in Asia/Tokyo timezone
3. `temperature_2m` (float32) - Temperature at 2m height (°C)
4. `relative_humidity_2m` (float32) - Relative humidity (%)
5. `rain` (float32) - Precipitation as rain (mm/hour)
6. `snowfall` (float32) - Precipitation as snow (mm/hour)
7. `wind_speed_10m` (float32) - Wind speed at 10m height (km/h)
8. `wind_gusts_10m` (float32) - Wind gusts at 10m height (km/h)
9. `cloud_cover_low` (float32) - Low cloud cover (%)
10. `is_day` (boolean) - Day/night flag

## Common Commands

### Full Training Pipeline
```bash
python -m sequence.main --config default
```

### Quick Testing (1 month, 1 route, 10 epochs)
```bash
python -m sequence.main --config quick
```

### Production Training (12 months, 50 routes, 100 epochs)
```bash
python -m sequence.main --config production
```

### Custom Configuration
```bash
python -m sequence.main --epochs 25 --max-routes 5 --batch-size 64
```

### Data Processing
Only run when you have new raw CSV data:
```bash
python preprocessors/process_traffic.py
```

### Weather Data Collection
Collect weather data for all 128 IC/JCT locations:
```bash
python scripts/fetch_weather_data.py
```

### Testing and Quality
```bash
pytest                    # Run tests
black sequence/           # Format code
flake8 sequence/          # Check code style
```

### Jupyter Analysis
```bash
jupyter lab Sequence_Apple.ipynb
```

## Key Architecture Patterns

### MLX Integration
All models use Apple's MLX framework for hardware acceleration. The system automatically detects Apple Silicon and optimizes accordingly.

### Enhanced Dual-Stream LSTM Architecture
The system uses a single MLX LSTM model with:
- **Duration Stream**: Traffic duration sequences
- **Feature Stream**: Weather, temporal, and infrastructure features (14 enhanced features)
- **Fusion Layer**: Combines both streams for prediction

### ML Features (14 total) - Weather-Enhanced
All features are extracted during preprocessing with weather data integration:

**Temporal Features (5 features):**
1. `hour` - Hour of day (0-23)
2. `day_of_week` - Day of week (0-6)
3. `is_weekend` - Weekend flag
4. `is_rush_hour` - Rush hour flag (7-9 AM, 5-7 PM)
5. `is_holiday` - Holiday flag (calendar integration)

**Weather Features (8 features) - Route-level weather integration:**
6. `has_precipitation` - Boolean precipitation flag (legacy)
7. `max_precip` - Maximum precipitation value (legacy)
8. `temp_start` - Temperature at route start IC (°C)
9. `temp_end` - Temperature at route end IC (°C)
10. `precip_start` - Total precipitation at start IC (mm/hour)
11. `precip_end` - Total precipitation at end IC (mm/hour)
12. `wind_speed_start` - Wind speed at start IC (km/h)
13. `wind_speed_end` - Wind speed at end IC (km/h)

**Infrastructure Features (1 feature):**
14. `speed_profile_numeric` - Highway speed limit from highway_data.json

**Weather Integration Details:**
- Weather data loaded from individual IC parquet files (`data/raw/weather/<ic_name>/YYYY.parquet`)
- Route-level features: Weather at both `ic_name_start` and `ic_name_end` locations
- Temporal matching: Hourly weather data matched to traffic record timestamps
- Fallback handling: Default values used when weather data unavailable

### Configuration-Driven Design
Three configuration presets handle different use cases:
- `DEFAULT_CONFIG`: Balanced for development (2 months, 2 routes)
- `QUICK_TEST_CONFIG`: Fast testing (1 month, 1 route)
- `PRODUCTION_CONFIG`: Full-scale deployment (12 months, 50 routes)

### Weather Data Schema

Weather data is stored in individual parquet files per IC location (`data/raw/weather/<ic_name>/YYYY.parquet`) with year-based files:

**Data Coverage:**
- **Locations**: 128 IC/JCT locations across Japanese highways  
- **Time Range**: 2024 (full year: 8,784 hourly records) + 2025 (up to yesterday)
- **Total Records**: ~1.1M+ weather observations
- **Source**: Open-Meteo Historical Weather API
- **Usage**: Route-specific loading during training (load weather for start_ic + end_ic only)
- **File Structure**: Separate parquet files per year per location (e.g., `あきる野ＩＣ/2024.parquet`, `あきる野ＩＣ/2025.parquet`)

### Road Event Integration
The system is designed to integrate road event data (accidents, construction, closures) with traffic predictions using decay functions and impact scoring.

## Data Flow

1. **Raw Data**: CSV files in `data/raw/traffic/YYYYMM/DD/`
2. **Preprocessing**: `process_traffic.py` creates `data/processed/traffic/traffic_YYYYMM.parquet` with all ML features
3. **Data Loading**: `data_loader.py` loads parquet files for training
4. **Training**: `trainer.py` trains dual-stream LSTM models with MLX
5. **Prediction**: `predictor.py` generates traffic forecasts with confidence intervals

## Experiment Tracking

MLflow experiments are stored in `mlruns/`. Each training run logs:
- Model architecture and hyperparameters
- Training/validation metrics (RMSE, MAE, R²)
- Route-specific performance
- Hardware utilization (Apple Silicon optimization)

## Important Notes

- The system uses only enhanced models - no basic models
- All ML features are pre-extracted during preprocessing with weather integration
- MLX requires Apple Silicon hardware for optimization
- Road event integration is documented in `ROAD_EVENT_INTEGRATION.md`
- Data paths were recently refactored - see `PATH_UPDATE_SUMMARY.md` for details

## Recent Updates

### Stage 1: Weather Integration (Completed 2025-01-07)
- ✅ **Enhanced Features**: Expanded from 8 → 14 ML features with weather integration
- ✅ **Weather Data**: Route-level weather features for start/end IC locations
- ✅ **Data Optimization**: Column cleanup (44 → ~31 columns) for improved efficiency
- ✅ **Architecture**: Updated MLX LSTM model to handle 14-feature input
- 📊 **Status**: Ready for validation testing with weather-enhanced predictions

**Next**: Process all traffic data with weather integration: `python preprocessors/process_traffic.py`