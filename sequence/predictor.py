"""
Prediction module for NEXCO traffic forecasting.
Handles inference using trained MLX models.
"""

import mlx.core as mx
import numpy as np
import pandas as pd
from typing import Dict, Tuple, Optional, Union
from sklearn.preprocessing import MinMaxScaler
from datetime import datetime
import polars as pl

from .models import MLXLSTMModel, Stage2HierarchicalModel
from .explainer import MLXModelWrapper, TrafficSHAPExplainer, SHAPAnalysisReport


class TrafficPredictor:
    def __init__(self, models: Dict[str, Tuple] = None, events_df: Optional[pl.DataFrame] = None,
                 enable_shap: bool = False):
        self.models = models or {}
        self.events_df = events_df  # For Stage 2 event impact calculations
        self.enable_shap = enable_shap
        self.shap_explainers = {}  # Cache for SHAP explainers
        
    def load_models(self, models: Dict[str, Tuple]):
        self.models = models
        print(f"Loaded {len(self.models)} trained models")
        
    def predict_traffic(self, route_name: str, recent_data: np.ndarray, 
                       recent_features: Optional[np.ndarray] = None) -> Optional[np.ndarray]:
        if route_name not in self.models:
            print(f"Model not found for route: {route_name}")
            return None
        
        model_data = self.models[route_name]
        
        # Handle different model storage formats
        if len(model_data) == 2:
            # Basic model format (model, duration_scaler)
            model, scaler_duration = model_data
            return self._predict_basic(model, scaler_duration, recent_data)
        elif len(model_data) == 3:
            # Enhanced model format (model, duration_scaler, feature_scaler)
            model, scaler_duration, scaler_features = model_data
            if recent_features is None:
                print("Enhanced model requires feature data")
                return None
            return self._predict_enhanced(model, scaler_duration, scaler_features, 
                                        recent_data, recent_features)
        else:
            print(f"Invalid model format for route: {route_name}")
            return None
    
    def predict_traffic_with_events(self, route_name: str, recent_data: np.ndarray,
                                   recent_features: np.ndarray, route_start_ic: str,
                                   route_end_ic: str, prediction_time: datetime) -> Optional[np.ndarray]:
        """
        Stage 2 prediction with event impact layer.
        
        Args:
            route_name: Route identifier
            recent_data: Historical traffic duration data
            recent_features: Historical feature data
            route_start_ic: Starting IC name
            route_end_ic: Ending IC name
            prediction_time: Time for prediction
            
        Returns:
            Event-adjusted traffic predictions
        """
        if route_name not in self.models:
            print(f"Model not found for route: {route_name}")
            return None
        
        model_data = self.models[route_name]
        
        if len(model_data) != 3:
            print("Stage 2 prediction requires enhanced model with 3 components")
            return None
        
        model, scaler_duration, scaler_features = model_data
        
        # Check if model supports Stage 2 (event impact)
        if isinstance(model, Stage2HierarchicalModel):
            return self._predict_stage2(model, scaler_duration, scaler_features,
                                      recent_data, recent_features, route_start_ic,
                                      route_end_ic, prediction_time)
        else:
            # Fallback to enhanced prediction if not Stage 2 model
            print("Model doesn't support event impact - using enhanced prediction")
            return self._predict_enhanced(model, scaler_duration, scaler_features,
                                        recent_data, recent_features)
    
    # Predict using basic model (duration only)
    def _predict_basic(self, model, scaler_duration: MinMaxScaler, 
                      recent_data: np.ndarray) -> np.ndarray:
        # Scale recent data
        scaled_data = scaler_duration.transform(recent_data.reshape(-1, 1)).flatten()
        
        # Ensure we have exactly the required input sequence length
        input_sequence = self._prepare_input_sequence(scaled_data, 288)
        
        # Convert to MLX array
        X_pred = mx.array(input_sequence.reshape(1, 288, 1))
        
        # Make prediction
        prediction = model(X_pred)
        
        # Rescale prediction
        prediction_np = np.array(prediction).reshape(-1, 1)
        prediction_rescaled = scaler_duration.inverse_transform(prediction_np).flatten()
        
        return prediction_rescaled
    
    # Predict using enhanced model (duration + features)
    def _predict_enhanced(self, model, scaler_duration: MinMaxScaler, 
                         scaler_features: MinMaxScaler, recent_data: np.ndarray,
                         recent_features: np.ndarray) -> np.ndarray:
        # Scale data
        duration_scaled = scaler_duration.transform(recent_data.reshape(-1, 1)).flatten()
        features_scaled = scaler_features.transform(recent_features)
        
        # Prepare input sequences (assuming 144 steps for enhanced model)
        duration_sequence = self._prepare_input_sequence(duration_scaled, 144)
        feature_sequence = self._prepare_feature_sequence(features_scaled, 144)
        
        # Convert to MLX arrays
        X_dur = mx.array(duration_sequence.reshape(1, 144, 1))
        X_feat = mx.array(feature_sequence.reshape(1, 144, -1))
        
        # Make prediction
        prediction = model(X_dur, X_feat)
        
        # Rescale prediction
        prediction_np = np.array(prediction).reshape(-1, 1)
        prediction_rescaled = scaler_duration.inverse_transform(prediction_np).flatten()
        
        return prediction_rescaled
    
    def _predict_stage2(self, model: Stage2HierarchicalModel, scaler_duration: MinMaxScaler,
                       scaler_features: MinMaxScaler, recent_data: np.ndarray,
                       recent_features: np.ndarray, route_start_ic: str, 
                       route_end_ic: str, prediction_time: datetime) -> np.ndarray:
        """Predict using Stage 2 hierarchical model with event impact."""
        # Scale data
        duration_scaled = scaler_duration.transform(recent_data.reshape(-1, 1)).flatten()
        features_scaled = scaler_features.transform(recent_features)
        
        # Prepare input sequences (assuming 144 steps for enhanced model)
        duration_sequence = self._prepare_input_sequence(duration_scaled, 144)
        feature_sequence = self._prepare_feature_sequence(features_scaled, 144)
        
        # Convert to MLX arrays
        X_dur = mx.array(duration_sequence.reshape(1, 144, 1))
        X_feat = mx.array(feature_sequence.reshape(1, 144, -1))
        
        # Make prediction with event impact
        prediction = model(X_dur, X_feat, route_start_ic, route_end_ic, 
                         prediction_time, self.events_df)
        
        # Rescale prediction
        prediction_np = np.array(prediction).reshape(-1, 1)
        prediction_rescaled = scaler_duration.inverse_transform(prediction_np).flatten()
        
        return prediction_rescaled
    
    def _prepare_input_sequence(self, scaled_data: np.ndarray, required_length: int) -> np.ndarray:
        if len(scaled_data) >= required_length:
            return scaled_data[-required_length:]
        else:
            # Pad with mean if not enough data
            mean_val = np.mean(scaled_data)
            padding = np.full(required_length - len(scaled_data), mean_val)
            return np.concatenate([padding, scaled_data])
    
    def _prepare_feature_sequence(self, scaled_features: np.ndarray, required_length: int) -> np.ndarray:
        if len(scaled_features) >= required_length:
            return scaled_features[-required_length:]
        else:
            # Pad with mean values for each feature
            mean_vals = np.mean(scaled_features, axis=0)
            padding_shape = (required_length - len(scaled_features), scaled_features.shape[1])
            padding = np.tile(mean_vals, (padding_shape[0], 1))
            return np.concatenate([padding, scaled_features])
    
    def predict_batch(self, route_predictions: Dict[str, Dict]) -> Dict[str, Optional[np.ndarray]]:
        results = {}
        
        for route_name, data in route_predictions.items():
            recent_data = data.get('recent_data')
            recent_features = data.get('recent_features')
            
            if recent_data is not None:
                prediction = self.predict_traffic(route_name, recent_data, recent_features)
                results[route_name] = prediction
            else:
                results[route_name] = None
                
        return results
    
    def get_available_routes(self) -> list:
        return list(self.models.keys())
    
    def get_model_info(self, route_name: str) -> Optional[Dict]:
        if route_name not in self.models:
            return None
            
        model_data = self.models[route_name]
        
        info = {
            'route': route_name,
            'model_type': 'enhanced' if len(model_data) == 3 else 'basic',
            'input_features': len(model_data) - 1,  # Exclude model itself
        }
        
        return info
    
    def predict_with_confidence(self, route_name: str, recent_data: np.ndarray,
                               recent_features: Optional[np.ndarray] = None,
                               num_samples: int = 10) -> Optional[Dict]:
        if route_name not in self.models:
            return None
            
        predictions = []
        
        # Generate multiple predictions (Monte Carlo approach)
        for _ in range(num_samples):
            pred = self.predict_traffic(route_name, recent_data, recent_features)
            if pred is not None:
                predictions.append(pred)
        
        if not predictions:
            return None
            
        predictions = np.array(predictions)
        
        result = {
            'mean_prediction': np.mean(predictions, axis=0),
            'std_prediction': np.std(predictions, axis=0),
            'confidence_95_lower': np.percentile(predictions, 2.5, axis=0),
            'confidence_95_upper': np.percentile(predictions, 97.5, axis=0),
            'num_samples': len(predictions)
        }
        
        return result

    def setup_shap_explainer(self, route_name: str, X_dur_background: np.ndarray,
                            X_feat_background: np.ndarray,
                            explainer_type: str = 'kernel') -> bool:
        """
        Set up SHAP explainer for a specific route.

        Args:
            route_name: Route identifier
            X_dur_background: Background duration data
            X_feat_background: Background feature data
            explainer_type: Type of SHAP explainer

        Returns:
            True if setup successful, False otherwise
        """
        if route_name not in self.models:
            print(f"Model not found for route: {route_name}")
            return False

        model_data = self.models[route_name]
        if len(model_data) != 3:
            print("SHAP requires enhanced model with 3 components")
            return False

        model, scaler_duration, scaler_features = model_data

        try:
            # Create model wrapper
            wrapper = MLXModelWrapper(
                model=model,
                scaler_duration=scaler_duration,
                scaler_features=scaler_features,
                input_steps=144  # Default from config
            )

            # Create SHAP explainer
            explainer = TrafficSHAPExplainer(wrapper)

            # Prepare background data
            background_data = explainer.prepare_background_data(
                X_dur_background, X_feat_background, n_background=50
            )

            # Fit explainer
            explainer.fit_explainer(background_data, explainer_type)

            # Cache the explainer
            self.shap_explainers[route_name] = explainer

            print(f"✅ SHAP explainer setup complete for {route_name}")
            return True

        except Exception as e:
            print(f"❌ Failed to setup SHAP explainer for {route_name}: {e}")
            return False

    def predict_with_explanation(self, route_name: str, recent_data: np.ndarray,
                                recent_features: np.ndarray,
                                generate_plots: bool = False,
                                save_dir: str = None) -> Dict:
        """
        Make prediction with SHAP explanation.

        Args:
            route_name: Route identifier
            recent_data: Recent duration data
            recent_features: Recent feature data
            generate_plots: Whether to generate SHAP plots
            save_dir: Directory to save plots

        Returns:
            Dictionary containing prediction and explanation
        """
        # Make regular prediction
        prediction = self.predict_traffic(route_name, recent_data, recent_features)

        result = {
            'route_name': route_name,
            'prediction': prediction,
            'explanation': None,
            'feature_importance': None,
            'shap_available': False
        }

        # Add SHAP explanation if enabled and explainer is available
        if self.enable_shap and route_name in self.shap_explainers:
            try:
                explainer = self.shap_explainers[route_name]

                # Prepare input data
                X_dur = recent_data.reshape(1, -1, 1)
                X_feat = recent_features.reshape(1, -1, recent_features.shape[-1])

                # Generate SHAP values
                shap_values = explainer.explain_prediction(X_dur, X_feat, max_evals=50)

                # Get feature importance
                feature_importance = explainer.get_feature_importance(shap_values)

                # Generate report if requested
                if generate_plots and save_dir:
                    report_generator = SHAPAnalysisReport(explainer)
                    explanation_report = report_generator.generate_report(
                        X_dur, X_feat, route_name, save_dir
                    )
                    result['explanation'] = explanation_report

                result['feature_importance'] = feature_importance
                result['shap_values'] = shap_values
                result['shap_available'] = True

                print(f"🔍 SHAP explanation generated for {route_name}")

            except Exception as e:
                print(f"⚠️ Failed to generate SHAP explanation: {e}")

        return result

    def get_feature_importance_summary(self, route_name: str = None) -> Dict:
        """
        Get feature importance summary for routes with SHAP explainers.

        Args:
            route_name: Specific route name, or None for all routes

        Returns:
            Dictionary of feature importance summaries
        """
        if route_name:
            routes = [route_name] if route_name in self.shap_explainers else []
        else:
            routes = list(self.shap_explainers.keys())

        summary = {}
        for route in routes:
            explainer = self.shap_explainers[route]
            if explainer.shap_values is not None:
                importance = explainer.get_feature_importance()
                # Get top 5 features
                top_features = sorted(importance.items(),
                                    key=lambda x: abs(x[1]), reverse=True)[:5]
                summary[route] = {
                    'top_features': top_features,
                    'total_features': len(importance)
                }

        return summary


class PredictionAnalyzer:

    @staticmethod
    def analyze_prediction_accuracy(predictions: np.ndarray, actuals: np.ndarray) -> Dict:
        """Analyze prediction accuracy metrics."""
        mae = np.mean(np.abs(predictions - actuals))
        mse = np.mean((predictions - actuals) ** 2)
        rmse = np.sqrt(mse)
        mape = np.mean(np.abs((predictions - actuals) / actuals)) * 100
        
        return {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'mape': mape,
            'mean_actual': np.mean(actuals),
            'mean_predicted': np.mean(predictions)
        }
    
    @staticmethod
    def format_prediction_summary(prediction: np.ndarray, route_name: str) -> str:
        """Format prediction results as readable summary."""
        if prediction is None:
            return f"No prediction available for {route_name}"
            
        summary = f"\n🎯 Traffic Prediction for {route_name}:\n"
        summary += f"Next 12 time steps:\n"
        
        for i, pred in enumerate(prediction):
            minutes = pred / 60
            summary += f"  Step {i+1}: {pred:.1f}s ({minutes:.1f} min)\n"
            
        avg_duration = np.mean(prediction)
        summary += f"\nAverage predicted duration: {avg_duration:.1f}s ({avg_duration/60:.1f} min)"
        
        return summary
    
    @staticmethod
    def compare_routes(predictions: Dict[str, np.ndarray]) -> Dict:
        """Compare predictions across multiple routes."""
        comparison = {}
        
        for route_name, pred in predictions.items():
            if pred is not None:
                comparison[route_name] = {
                    'avg_duration_seconds': np.mean(pred),
                    'avg_duration_minutes': np.mean(pred) / 60,
                    'min_duration': np.min(pred),
                    'max_duration': np.max(pred),
                    'std_duration': np.std(pred)
                }
        
        return comparison