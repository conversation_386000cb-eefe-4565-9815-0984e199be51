"""
Prediction module for NEXCO traffic forecasting.
Handles inference using trained MLX models.
"""

import mlx.core as mx
import numpy as np
import pandas as pd
from typing import Dict, Tu<PERSON>, Optional, Union
from sklearn.preprocessing import MinMaxScaler
from datetime import datetime
import polars as pl

from .models import MLXLSTMModel, Stage2HierarchicalModel


class TrafficPredictor:    
    def __init__(self, models: Dict[str, Tuple] = None, events_df: Optional[pl.DataFrame] = None):
        self.models = models or {}
        self.events_df = events_df  # For Stage 2 event impact calculations
        
    def load_models(self, models: Dict[str, Tuple]):
        self.models = models
        print(f"Loaded {len(self.models)} trained models")
        
    def predict_traffic(self, route_name: str, recent_data: np.ndarray, 
                       recent_features: Optional[np.ndarray] = None) -> Optional[np.ndarray]:
        if route_name not in self.models:
            print(f"Model not found for route: {route_name}")
            return None
        
        model_data = self.models[route_name]
        
        # Handle different model storage formats
        if len(model_data) == 2:
            # Basic model format (model, duration_scaler)
            model, scaler_duration = model_data
            return self._predict_basic(model, scaler_duration, recent_data)
        elif len(model_data) == 3:
            # Enhanced model format (model, duration_scaler, feature_scaler)
            model, scaler_duration, scaler_features = model_data
            if recent_features is None:
                print("Enhanced model requires feature data")
                return None
            return self._predict_enhanced(model, scaler_duration, scaler_features, 
                                        recent_data, recent_features)
        else:
            print(f"Invalid model format for route: {route_name}")
            return None
    
    def predict_traffic_with_events(self, route_name: str, recent_data: np.ndarray,
                                   recent_features: np.ndarray, route_start_ic: str,
                                   route_end_ic: str, prediction_time: datetime) -> Optional[np.ndarray]:
        """
        Stage 2 prediction with event impact layer.
        
        Args:
            route_name: Route identifier
            recent_data: Historical traffic duration data
            recent_features: Historical feature data
            route_start_ic: Starting IC name
            route_end_ic: Ending IC name
            prediction_time: Time for prediction
            
        Returns:
            Event-adjusted traffic predictions
        """
        if route_name not in self.models:
            print(f"Model not found for route: {route_name}")
            return None
        
        model_data = self.models[route_name]
        
        if len(model_data) != 3:
            print("Stage 2 prediction requires enhanced model with 3 components")
            return None
        
        model, scaler_duration, scaler_features = model_data
        
        # Check if model supports Stage 2 (event impact)
        if isinstance(model, Stage2HierarchicalModel):
            return self._predict_stage2(model, scaler_duration, scaler_features,
                                      recent_data, recent_features, route_start_ic,
                                      route_end_ic, prediction_time)
        else:
            # Fallback to enhanced prediction if not Stage 2 model
            print("Model doesn't support event impact - using enhanced prediction")
            return self._predict_enhanced(model, scaler_duration, scaler_features,
                                        recent_data, recent_features)
    
    # Predict using basic model (duration only)
    def _predict_basic(self, model, scaler_duration: MinMaxScaler, 
                      recent_data: np.ndarray) -> np.ndarray:
        # Scale recent data
        scaled_data = scaler_duration.transform(recent_data.reshape(-1, 1)).flatten()
        
        # Ensure we have exactly the required input sequence length
        input_sequence = self._prepare_input_sequence(scaled_data, 288)
        
        # Convert to MLX array
        X_pred = mx.array(input_sequence.reshape(1, 288, 1))
        
        # Make prediction
        prediction = model(X_pred)
        
        # Rescale prediction
        prediction_np = np.array(prediction).reshape(-1, 1)
        prediction_rescaled = scaler_duration.inverse_transform(prediction_np).flatten()
        
        return prediction_rescaled
    
    # Predict using enhanced model (duration + features)
    def _predict_enhanced(self, model, scaler_duration: MinMaxScaler, 
                         scaler_features: MinMaxScaler, recent_data: np.ndarray,
                         recent_features: np.ndarray) -> np.ndarray:
        # Scale data
        duration_scaled = scaler_duration.transform(recent_data.reshape(-1, 1)).flatten()
        features_scaled = scaler_features.transform(recent_features)
        
        # Prepare input sequences (assuming 144 steps for enhanced model)
        duration_sequence = self._prepare_input_sequence(duration_scaled, 144)
        feature_sequence = self._prepare_feature_sequence(features_scaled, 144)
        
        # Convert to MLX arrays
        X_dur = mx.array(duration_sequence.reshape(1, 144, 1))
        X_feat = mx.array(feature_sequence.reshape(1, 144, -1))
        
        # Make prediction
        prediction = model(X_dur, X_feat)
        
        # Rescale prediction
        prediction_np = np.array(prediction).reshape(-1, 1)
        prediction_rescaled = scaler_duration.inverse_transform(prediction_np).flatten()
        
        return prediction_rescaled
    
    def _predict_stage2(self, model: Stage2HierarchicalModel, scaler_duration: MinMaxScaler,
                       scaler_features: MinMaxScaler, recent_data: np.ndarray,
                       recent_features: np.ndarray, route_start_ic: str, 
                       route_end_ic: str, prediction_time: datetime) -> np.ndarray:
        """Predict using Stage 2 hierarchical model with event impact."""
        # Scale data
        duration_scaled = scaler_duration.transform(recent_data.reshape(-1, 1)).flatten()
        features_scaled = scaler_features.transform(recent_features)
        
        # Prepare input sequences (assuming 144 steps for enhanced model)
        duration_sequence = self._prepare_input_sequence(duration_scaled, 144)
        feature_sequence = self._prepare_feature_sequence(features_scaled, 144)
        
        # Convert to MLX arrays
        X_dur = mx.array(duration_sequence.reshape(1, 144, 1))
        X_feat = mx.array(feature_sequence.reshape(1, 144, -1))
        
        # Make prediction with event impact
        prediction = model(X_dur, X_feat, route_start_ic, route_end_ic, 
                         prediction_time, self.events_df)
        
        # Rescale prediction
        prediction_np = np.array(prediction).reshape(-1, 1)
        prediction_rescaled = scaler_duration.inverse_transform(prediction_np).flatten()
        
        return prediction_rescaled
    
    def _prepare_input_sequence(self, scaled_data: np.ndarray, required_length: int) -> np.ndarray:
        if len(scaled_data) >= required_length:
            return scaled_data[-required_length:]
        else:
            # Pad with mean if not enough data
            mean_val = np.mean(scaled_data)
            padding = np.full(required_length - len(scaled_data), mean_val)
            return np.concatenate([padding, scaled_data])
    
    def _prepare_feature_sequence(self, scaled_features: np.ndarray, required_length: int) -> np.ndarray:
        if len(scaled_features) >= required_length:
            return scaled_features[-required_length:]
        else:
            # Pad with mean values for each feature
            mean_vals = np.mean(scaled_features, axis=0)
            padding_shape = (required_length - len(scaled_features), scaled_features.shape[1])
            padding = np.tile(mean_vals, (padding_shape[0], 1))
            return np.concatenate([padding, scaled_features])
    
    def predict_batch(self, route_predictions: Dict[str, Dict]) -> Dict[str, Optional[np.ndarray]]:
        results = {}
        
        for route_name, data in route_predictions.items():
            recent_data = data.get('recent_data')
            recent_features = data.get('recent_features')
            
            if recent_data is not None:
                prediction = self.predict_traffic(route_name, recent_data, recent_features)
                results[route_name] = prediction
            else:
                results[route_name] = None
                
        return results
    
    def get_available_routes(self) -> list:
        return list(self.models.keys())
    
    def get_model_info(self, route_name: str) -> Optional[Dict]:
        if route_name not in self.models:
            return None
            
        model_data = self.models[route_name]
        
        info = {
            'route': route_name,
            'model_type': 'enhanced' if len(model_data) == 3 else 'basic',
            'input_features': len(model_data) - 1,  # Exclude model itself
        }
        
        return info
    
    def predict_with_confidence(self, route_name: str, recent_data: np.ndarray,
                               recent_features: Optional[np.ndarray] = None,
                               num_samples: int = 10) -> Optional[Dict]:
        if route_name not in self.models:
            return None
            
        predictions = []
        
        # Generate multiple predictions (Monte Carlo approach)
        for _ in range(num_samples):
            pred = self.predict_traffic(route_name, recent_data, recent_features)
            if pred is not None:
                predictions.append(pred)
        
        if not predictions:
            return None
            
        predictions = np.array(predictions)
        
        result = {
            'mean_prediction': np.mean(predictions, axis=0),
            'std_prediction': np.std(predictions, axis=0),
            'confidence_95_lower': np.percentile(predictions, 2.5, axis=0),
            'confidence_95_upper': np.percentile(predictions, 97.5, axis=0),
            'num_samples': len(predictions)
        }
        
        return result


class PredictionAnalyzer:

    @staticmethod
    def analyze_prediction_accuracy(predictions: np.ndarray, actuals: np.ndarray) -> Dict:
        """Analyze prediction accuracy metrics."""
        mae = np.mean(np.abs(predictions - actuals))
        mse = np.mean((predictions - actuals) ** 2)
        rmse = np.sqrt(mse)
        mape = np.mean(np.abs((predictions - actuals) / actuals)) * 100
        
        return {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'mape': mape,
            'mean_actual': np.mean(actuals),
            'mean_predicted': np.mean(predictions)
        }
    
    @staticmethod
    def format_prediction_summary(prediction: np.ndarray, route_name: str) -> str:
        """Format prediction results as readable summary."""
        if prediction is None:
            return f"No prediction available for {route_name}"
            
        summary = f"\n🎯 Traffic Prediction for {route_name}:\n"
        summary += f"Next 12 time steps:\n"
        
        for i, pred in enumerate(prediction):
            minutes = pred / 60
            summary += f"  Step {i+1}: {pred:.1f}s ({minutes:.1f} min)\n"
            
        avg_duration = np.mean(prediction)
        summary += f"\nAverage predicted duration: {avg_duration:.1f}s ({avg_duration/60:.1f} min)"
        
        return summary
    
    @staticmethod
    def compare_routes(predictions: Dict[str, np.ndarray]) -> Dict:
        """Compare predictions across multiple routes."""
        comparison = {}
        
        for route_name, pred in predictions.items():
            if pred is not None:
                comparison[route_name] = {
                    'avg_duration_seconds': np.mean(pred),
                    'avg_duration_minutes': np.mean(pred) / 60,
                    'min_duration': np.min(pred),
                    'max_duration': np.max(pred),
                    'std_duration': np.std(pred)
                }
        
        return comparison