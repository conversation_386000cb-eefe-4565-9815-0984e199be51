"""
NEXCO Traffic Prediction System

A modular system for processing traffic data and training LSTM models
optimized for Apple Silicon using the MLX framework.
"""

# Always available - no heavy dependencies
from .config import Config, DEFAULT_CONFIG, QUICK_TEST_CONFIG, PRODUCTION_CONFIG

__version__ = "0.1.0"
__author__ = "NEXCO Traffic Analysis Team"

# Lazy imports for heavy dependencies
def _lazy_import():
    """Lazy import of ML components to avoid loading dependencies unnecessarily."""
    global TrafficDataLoader, MLXLSTMModel
    global SequenceGenerator, ModelFactory, loss_fn
    global Trainer, TrafficPredictor, PredictionAnalyzer, NEXCOTrafficSystem
    
    from .data_loader import TrafficDataLoader
    from .models import (
        MLXLSTMModel, 
        SequenceGenerator, 
        ModelFactory,
        loss_fn
    )
    from .trainer import Trainer
    from .predictor import TrafficPredictor, PredictionAnalyzer
    from .main import NEXCOTrafficSystem

# Make components available through __getattr__ (Python 3.7+)
def __getattr__(name):
    if name in [
        'TrafficDataLoader', 'MLXLSTMModel',
        'SequenceGenerator', 'ModelFactory', 'loss_fn',
        'Trainer', 'TrafficPredictor', 'PredictionAnalyzer', 'NEXCOTrafficSystem'
    ]:
        _lazy_import()
        return globals()[name]
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

__all__ = [
    # Configuration
    "Config",
    "DEFAULT_CONFIG", 
    "QUICK_TEST_CONFIG", 
    "PRODUCTION_CONFIG",
    
    # Data Loading
    "TrafficDataLoader",
    
    # Models
    "MLXLSTMModel",
    "SequenceGenerator",
    "ModelFactory",
    "loss_fn",
    
    # Training
    "Trainer",
    
    # Prediction
    "TrafficPredictor",
    "PredictionAnalyzer",
    
    # Main System
    "NEXCOTrafficSystem",
]