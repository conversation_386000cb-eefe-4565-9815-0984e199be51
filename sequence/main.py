import argparse
import sys
from pathlib import Path
from typing import Dict, Any

from .config import Config, DEFAULT_CONFIG, QUICK_TEST_CONFIG, PRODUCTION_CONFIG
from .data_loader import TrafficDataLoader
from .trainer import Trainer
from .predictor import TrafficPredictor, PredictionAnalyzer


class NEXCOTrafficSystem:
    def __init__(self, config: Config = None):
        self.config = config or DEFAULT_CONFIG
        self.data_loader = TrafficDataLoader(
            data_path=self.config.data.output_path
        )
        self.trainer = Trainer(self.config.get_training_config_dict())
        self.predictor = TrafficPredictor(enable_shap=self.config.shap.enable_shap)
        self.trained_models = {}
        self.training_data = None  # Store for SHAP background data
        
    def verify_data(self):
        """Verify processed traffic data is available."""
        print("📊 Verifying traffic data...")
        if self.data_loader.verify_data_availability():
            print("✅ Data verification completed!")
            return True
        else:
            print("❌ Data verification failed!")
            print("💡 Please run: python preprocessors/process_traffic.py")
            return False
        
    def load_data(self):
        """Load processed traffic data for training."""
        print("📁 Loading processed data...")
        df = self.data_loader.load_data(self.config.data.months_to_load)
        self.training_data = df  # Store for SHAP
        return df
        
    def train_models(self, df):
        """Train traffic prediction models."""
        print("🚀 Training models...")
        trained_models = self.trainer.train_multiple_routes(
            df, 
            max_routes=self.config.prediction.max_routes_training
        )
        
        self.trained_models = trained_models
        self.predictor.load_models(trained_models)
        
        return trained_models
        
    def run_full_pipeline(self):
        """Run the complete pipeline: verify data, train models."""
        print("🎯 Running NEXCO Traffic Prediction Pipeline...")
        print("=" * 60)
        
        # Verify processed data exists
        if not self.verify_data():
            return {}
        
        # Load data
        df = self.load_data()
        
        # Train models
        trained_models = self.train_models(df)
        
        print("=" * 60)
        print(f"✅ Pipeline completed! Trained {len(trained_models)} models")
        
        return trained_models
        
    def predict_route(self, route_name: str, recent_data, recent_features=None):
        """Make prediction for a specific route."""
        if not self.trained_models:
            print("❌ No trained models available. Run training first.")
            return None
            
        prediction = self.predictor.predict_traffic(route_name, recent_data, recent_features)
        
        if prediction is not None:
            summary = PredictionAnalyzer.format_prediction_summary(prediction, route_name)
            print(summary)
            
        return prediction

    def setup_shap_explainers(self):
        """Set up SHAP explainers for all trained models."""
        if not self.config.shap.enable_shap:
            print("📊 SHAP is disabled in configuration")
            return False

        if not self.trained_models:
            print("❌ No trained models available for SHAP setup")
            return False

        if self.training_data is None:
            print("❌ Training data not available for SHAP background")
            return False

        print("🔍 Setting up SHAP explainers...")

        # Prepare background data from training set
        from .data_loader import TrafficDataLoader
        loader = TrafficDataLoader()

        success_count = 0
        for route_name in self.trained_models.keys():
            print(f"  📊 Setting up SHAP for {route_name}...")

            # Get route-specific data for background
            route_data = self.training_data[
                (self.training_data['ic_name_start'] + ' to ' +
                 self.training_data['ic_name_end']) == route_name
            ].copy()

            if len(route_data) < 100:
                print(f"    ⚠️ Insufficient data for {route_name} ({len(route_data)} samples)")
                continue

            # Prepare sequences for SHAP background
            try:
                sequences = loader.create_sequences(
                    route_data,
                    input_steps=self.config.model.input_steps,
                    output_steps=self.config.model.output_steps
                )

                if sequences is None or len(sequences[0]) < 50:
                    print(f"    ⚠️ Insufficient sequences for {route_name}")
                    continue

                X_dur, X_feat, _ = sequences

                # Use subset for background
                n_bg = min(self.config.shap.background_samples, len(X_dur))
                X_dur_bg = X_dur[:n_bg]
                X_feat_bg = X_feat[:n_bg]

                # Setup SHAP explainer
                success = self.predictor.setup_shap_explainer(
                    route_name, X_dur_bg, X_feat_bg,
                    self.config.shap.explainer_type
                )

                if success:
                    success_count += 1

            except Exception as e:
                print(f"    ❌ Failed to setup SHAP for {route_name}: {e}")

        print(f"✅ SHAP setup completed for {success_count}/{len(self.trained_models)} models")
        return success_count > 0

    def predict_with_explanation(self, route_name: str, recent_data, recent_features=None,
                                generate_plots: bool = None, save_dir: str = None):
        """Make prediction with SHAP explanation."""
        if not self.trained_models:
            print("❌ No trained models available. Run training first.")
            return None

        # Use config defaults if not specified
        if generate_plots is None:
            generate_plots = self.config.shap.generate_plots
        if save_dir is None and self.config.shap.save_plots:
            save_dir = self.config.shap.plots_directory

        # Make prediction with explanation
        result = self.predictor.predict_with_explanation(
            route_name, recent_data, recent_features,
            generate_plots, save_dir
        )

        if result['prediction'] is not None:
            # Print regular prediction summary
            summary = PredictionAnalyzer.format_prediction_summary(
                result['prediction'], route_name
            )
            print(summary)

            # Print SHAP summary if available
            if result['shap_available'] and result['feature_importance']:
                print(f"\n🔍 SHAP Feature Importance for {route_name}:")
                importance = result['feature_importance']
                top_features = sorted(importance.items(),
                                    key=lambda x: abs(x[1]), reverse=True)[:5]

                for i, (feature, score) in enumerate(top_features, 1):
                    print(f"  {i}. {feature}: {score:.4f}")

        return result

    def get_system_info(self):
        """Get information about the system and available models."""
        info = {
            'config': self.config.to_dict(),
            'available_routes': self.predictor.get_available_routes(),
            'num_models': len(self.trained_models),
        }
        
        # Add model info for each route
        model_info = {}
        for route in self.predictor.get_available_routes():
            model_info[route] = self.predictor.get_model_info(route)
        info['model_details'] = model_info
        
        return info


def create_argument_parser():
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(description='NEXCO Traffic Prediction System')
    
    parser.add_argument('--config', type=str, choices=['default', 'quick', 'production'],
                       default='default', help='Configuration preset to use')
    
    parser.add_argument('--mode', type=str, choices=['train', 'predict', 'full'],
                       default='full', help='Mode to run the system in')
    
    parser.add_argument('--data-path', type=str, help='Path to raw traffic data')
    parser.add_argument('--output-path', type=str, help='Path to save processed data')
    
    parser.add_argument('--epochs', type=int, help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, help='Training batch size')
    parser.add_argument('--learning-rate', type=float, help='Learning rate for training')
    
    parser.add_argument('--max-routes', type=int, help='Maximum number of routes to train')
    parser.add_argument('--months-limit', type=int, help='Maximum number of months to process')
    
    parser.add_argument('--route', type=str, help='Specific route for prediction mode')

    # SHAP arguments
    parser.add_argument('--enable-shap', action='store_true',
                       help='Enable SHAP explanations')
    parser.add_argument('--shap-explainer', type=str, choices=['kernel', 'permutation'],
                       default='kernel', help='Type of SHAP explainer to use')
    parser.add_argument('--shap-plots', action='store_true',
                       help='Generate SHAP visualization plots')
    parser.add_argument('--shap-plots-dir', type=str, default='./shap_plots',
                       help='Directory to save SHAP plots')

    return parser


def main():
    """Main entry point."""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # Select configuration
    if args.config == 'quick':
        config = QUICK_TEST_CONFIG
    elif args.config == 'production':
        config = PRODUCTION_CONFIG
    else:
        config = DEFAULT_CONFIG
    
    # Override config with command line arguments
    # Note: data_path now refers to processed data output path
    if args.output_path:
        config.data.output_path = args.output_path
    if args.epochs:
        config.training.epochs = args.epochs
    if args.batch_size:
        config.training.batch_size = args.batch_size
    if args.learning_rate:
        config.training.learning_rate = args.learning_rate
    if args.max_routes:
        config.prediction.max_routes_training = args.max_routes
    if args.months_limit:
        config.data.months_limit = args.months_limit

    # SHAP configuration
    if args.enable_shap:
        config.shap.enable_shap = True
        config.shap.explainer_type = args.shap_explainer
        config.shap.generate_plots = args.shap_plots
        config.shap.plots_directory = args.shap_plots_dir

    # Initialize system
    system = NEXCOTrafficSystem(config)
    
    try:
        if args.mode == 'train':
            print("🏋️ Training mode selected")
            if system.verify_data():
                df = system.load_data()
                system.train_models(df)

                # Setup SHAP if enabled
                if config.shap.enable_shap:
                    system.setup_shap_explainers()
            else:
                return 1
            
        elif args.mode == 'predict':
            print("🔮 Prediction mode selected")
            if not args.route:
                print("❌ Route name required for prediction mode (--route)")
                return 1
            # Note: In a real implementation, you would load recent data here
            print("Note: Prediction requires recent traffic data to be provided")
            
        else:  # full mode
            print("🚀 Full pipeline mode selected")
            system.run_full_pipeline()

            # Setup SHAP if enabled
            if config.shap.enable_shap:
                system.setup_shap_explainers()
            
        return 0
        
    except KeyboardInterrupt:
        print("\n⛔ Process interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())