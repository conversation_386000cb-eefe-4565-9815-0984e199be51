import argparse
import sys
from pathlib import Path
from typing import Dict, Any

from .config import Config, DEFAULT_CONFIG, QUICK_TEST_CONFIG, PRODUCTION_CONFIG
from .data_loader import TrafficDataLoader
from .trainer import Trainer
from .predictor import TrafficPredictor, PredictionAnalyzer


class NEXCOTrafficSystem:
    def __init__(self, config: Config = None):
        self.config = config or DEFAULT_CONFIG
        self.data_loader = TrafficDataLoader(
            data_path=self.config.data.output_path
        )
        self.trainer = Trainer(self.config.get_training_config_dict())
        self.predictor = TrafficPredictor()
        self.trained_models = {}
        
    def verify_data(self):
        """Verify processed traffic data is available."""
        print("📊 Verifying traffic data...")
        if self.data_loader.verify_data_availability():
            print("✅ Data verification completed!")
            return True
        else:
            print("❌ Data verification failed!")
            print("💡 Please run: python preprocessors/process_traffic.py")
            return False
        
    def load_data(self):
        """Load processed traffic data for training."""
        print("📁 Loading processed data...")
        df = self.data_loader.load_data(self.config.data.months_to_load)
        return df
        
    def train_models(self, df):
        """Train traffic prediction models."""
        print("🚀 Training models...")
        trained_models = self.trainer.train_multiple_routes(
            df, 
            max_routes=self.config.prediction.max_routes_training
        )
        
        self.trained_models = trained_models
        self.predictor.load_models(trained_models)
        
        return trained_models
        
    def run_full_pipeline(self):
        """Run the complete pipeline: verify data, train models."""
        print("🎯 Running NEXCO Traffic Prediction Pipeline...")
        print("=" * 60)
        
        # Verify processed data exists
        if not self.verify_data():
            return {}
        
        # Load data
        df = self.load_data()
        
        # Train models
        trained_models = self.train_models(df)
        
        print("=" * 60)
        print(f"✅ Pipeline completed! Trained {len(trained_models)} models")
        
        return trained_models
        
    def predict_route(self, route_name: str, recent_data, recent_features=None):
        """Make prediction for a specific route."""
        if not self.trained_models:
            print("❌ No trained models available. Run training first.")
            return None
            
        prediction = self.predictor.predict_traffic(route_name, recent_data, recent_features)
        
        if prediction is not None:
            summary = PredictionAnalyzer.format_prediction_summary(prediction, route_name)
            print(summary)
            
        return prediction
        
    def get_system_info(self):
        """Get information about the system and available models."""
        info = {
            'config': self.config.to_dict(),
            'available_routes': self.predictor.get_available_routes(),
            'num_models': len(self.trained_models),
        }
        
        # Add model info for each route
        model_info = {}
        for route in self.predictor.get_available_routes():
            model_info[route] = self.predictor.get_model_info(route)
        info['model_details'] = model_info
        
        return info


def create_argument_parser():
    """Create command line argument parser."""
    parser = argparse.ArgumentParser(description='NEXCO Traffic Prediction System')
    
    parser.add_argument('--config', type=str, choices=['default', 'quick', 'production'],
                       default='default', help='Configuration preset to use')
    
    parser.add_argument('--mode', type=str, choices=['train', 'predict', 'full'],
                       default='full', help='Mode to run the system in')
    
    parser.add_argument('--data-path', type=str, help='Path to raw traffic data')
    parser.add_argument('--output-path', type=str, help='Path to save processed data')
    
    parser.add_argument('--epochs', type=int, help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, help='Training batch size')
    parser.add_argument('--learning-rate', type=float, help='Learning rate for training')
    
    parser.add_argument('--max-routes', type=int, help='Maximum number of routes to train')
    parser.add_argument('--months-limit', type=int, help='Maximum number of months to process')
    
    parser.add_argument('--route', type=str, help='Specific route for prediction mode')
    
    return parser


def main():
    """Main entry point."""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    # Select configuration
    if args.config == 'quick':
        config = QUICK_TEST_CONFIG
    elif args.config == 'production':
        config = PRODUCTION_CONFIG
    else:
        config = DEFAULT_CONFIG
    
    # Override config with command line arguments
    # Note: data_path now refers to processed data output path
    if args.output_path:
        config.data.output_path = args.output_path
    if args.epochs:
        config.training.epochs = args.epochs
    if args.batch_size:
        config.training.batch_size = args.batch_size
    if args.learning_rate:
        config.training.learning_rate = args.learning_rate
    if args.max_routes:
        config.prediction.max_routes_training = args.max_routes
    if args.months_limit:
        config.data.months_limit = args.months_limit
    
    # Initialize system
    system = NEXCOTrafficSystem(config)
    
    try:
        if args.mode == 'train':
            print("🏋️ Training mode selected")
            if system.verify_data():
                df = system.load_data()
                system.train_models(df)
            else:
                return 1
            
        elif args.mode == 'predict':
            print("🔮 Prediction mode selected")
            if not args.route:
                print("❌ Route name required for prediction mode (--route)")
                return 1
            # Note: In a real implementation, you would load recent data here
            print("Note: Prediction requires recent traffic data to be provided")
            
        else:  # full mode
            print("🚀 Full pipeline mode selected")
            system.run_full_pipeline()
            
        return 0
        
    except KeyboardInterrupt:
        print("\n⛔ Process interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())