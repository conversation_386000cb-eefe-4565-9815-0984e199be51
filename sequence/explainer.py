"""
SHAP-based explainability module for NEXCO traffic prediction models.
Provides model interpretability for MLX LSTM models using SHAP values.
"""

import shap
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import mlx.core as mx
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.preprocessing import MinMaxScaler
import warnings
warnings.filterwarnings('ignore')

from .models import MLXLSTMModel, Stage2HierarchicalModel


class MLXModelWrapper:
    """
    Wrapper class to make MLX models compatible with SHAP explainers.
    Handles the dual-stream architecture of MLXLSTMModel.
    """
    
    def __init__(self, model: Union[MLXLSTMModel, Stage2HierarchicalModel], 
                 scaler_duration: MinMaxScaler, scaler_features: MinMaxScaler,
                 input_steps: int = 144):
        """
        Initialize the wrapper.
        
        Args:
            model: Trained MLX model
            scaler_duration: Scaler for duration data
            scaler_features: Scaler for feature data
            input_steps: Number of input time steps
        """
        self.model = model
        self.scaler_duration = scaler_duration
        self.scaler_features = scaler_features
        self.input_steps = input_steps
        
    def predict(self, X: np.ndarray) -> np.ndarray:
        """
        Prediction function compatible with SHAP.
        
        Args:
            X: Combined input array of shape (batch_size, seq_len * (1 + n_features))
            
        Returns:
            Predictions as numpy array
        """
        batch_size = X.shape[0]
        
        # Split combined input back into duration and features
        # X is flattened: [dur_seq, feat_seq] for each sample
        total_features = X.shape[1] // self.input_steps
        n_features = total_features - 1  # Subtract 1 for duration
        
        # Reshape and split
        X_reshaped = X.reshape(batch_size, self.input_steps, total_features)
        X_dur = X_reshaped[:, :, 0:1]  # Duration column
        X_feat = X_reshaped[:, :, 1:]  # Feature columns
        
        # Convert to MLX arrays
        X_dur_mx = mx.array(X_dur)
        X_feat_mx = mx.array(X_feat)
        
        # Make prediction
        predictions = self.model(X_dur_mx, X_feat_mx)
        
        # Convert back to numpy
        return np.array(predictions)


class TrafficSHAPExplainer:
    """
    SHAP explainer for NEXCO traffic prediction models.
    Provides feature importance analysis and visualizations.
    """
    
    def __init__(self, model_wrapper: MLXModelWrapper, 
                 feature_names: List[str] = None):
        """
        Initialize the SHAP explainer.
        
        Args:
            model_wrapper: MLX model wrapper
            feature_names: Names of the features
        """
        self.model_wrapper = model_wrapper
        self.feature_names = feature_names or self._get_default_feature_names()
        self.explainer = None
        self.shap_values = None
        self.background_data = None
        
    def _get_default_feature_names(self) -> List[str]:
        """Get default feature names for NEXCO traffic prediction."""
        return [
            'duration_seconds',  # Duration stream
            # Temporal features (5 features)
            'hour', 'day_of_week', 'is_weekend', 'is_rush_hour', 'is_holiday',
            # Weather features (8 features)
            'has_precipitation', 'max_precip',
            'temp_start', 'temp_end',
            'precip_start', 'precip_end', 
            'wind_speed_start', 'wind_speed_end',
            # Infrastructure features (1 feature)
            'speed_profile_numeric'
        ]
    
    def prepare_background_data(self, X_dur: np.ndarray, X_feat: np.ndarray, 
                               n_background: int = 100) -> np.ndarray:
        """
        Prepare background data for SHAP explainer.
        
        Args:
            X_dur: Duration data (batch_size, seq_len, 1)
            X_feat: Feature data (batch_size, seq_len, n_features)
            n_background: Number of background samples
            
        Returns:
            Combined background data
        """
        # Sample background data
        n_samples = min(n_background, X_dur.shape[0])
        indices = np.random.choice(X_dur.shape[0], n_samples, replace=False)
        
        X_dur_bg = X_dur[indices]
        X_feat_bg = X_feat[indices]
        
        # Combine duration and features
        X_combined = np.concatenate([X_dur_bg, X_feat_bg], axis=2)
        
        # Flatten for SHAP (batch_size, seq_len * total_features)
        self.background_data = X_combined.reshape(n_samples, -1)
        
        return self.background_data
    
    def fit_explainer(self, background_data: np.ndarray = None, 
                     explainer_type: str = 'kernel') -> None:
        """
        Fit the SHAP explainer.
        
        Args:
            background_data: Background data for explainer
            explainer_type: Type of SHAP explainer ('kernel', 'permutation')
        """
        if background_data is not None:
            self.background_data = background_data
        
        if self.background_data is None:
            raise ValueError("Background data must be provided")
        
        if explainer_type == 'kernel':
            self.explainer = shap.KernelExplainer(
                self.model_wrapper.predict, 
                self.background_data
            )
        elif explainer_type == 'permutation':
            self.explainer = shap.PermutationExplainer(
                self.model_wrapper.predict,
                self.background_data
            )
        else:
            raise ValueError(f"Unsupported explainer type: {explainer_type}")
    
    def explain_prediction(self, X_dur: np.ndarray, X_feat: np.ndarray,
                          max_evals: int = 100) -> np.ndarray:
        """
        Generate SHAP values for given input.
        
        Args:
            X_dur: Duration data (batch_size, seq_len, 1)
            X_feat: Feature data (batch_size, seq_len, n_features)
            max_evals: Maximum evaluations for kernel explainer
            
        Returns:
            SHAP values
        """
        if self.explainer is None:
            raise ValueError("Explainer must be fitted first")
        
        # Combine and flatten input
        X_combined = np.concatenate([X_dur, X_feat], axis=2)
        X_flat = X_combined.reshape(X_combined.shape[0], -1)
        
        # Generate SHAP values
        if hasattr(self.explainer, 'shap_values'):
            # For KernelExplainer
            self.shap_values = self.explainer.shap_values(X_flat, nsamples=max_evals)
        else:
            # For PermutationExplainer
            self.shap_values = self.explainer(X_flat, max_evals=max_evals).values
        
        return self.shap_values
    
    def get_feature_importance(self, shap_values: np.ndarray = None,
                              aggregate_time: bool = True) -> Dict[str, float]:
        """
        Calculate feature importance from SHAP values.
        
        Args:
            shap_values: SHAP values to analyze
            aggregate_time: Whether to aggregate across time steps
            
        Returns:
            Dictionary of feature importance scores
        """
        if shap_values is None:
            shap_values = self.shap_values
        
        if shap_values is None:
            raise ValueError("SHAP values not available")
        
        if aggregate_time:
            # Reshape SHAP values back to (batch, seq_len, features)
            batch_size = shap_values.shape[0]
            seq_len = self.model_wrapper.input_steps
            n_features = len(self.feature_names)
            
            shap_reshaped = shap_values.reshape(batch_size, seq_len, n_features)
            
            # Aggregate across time and samples
            importance = np.mean(np.abs(shap_reshaped), axis=(0, 1))
        else:
            # Just aggregate across samples
            importance = np.mean(np.abs(shap_values), axis=0)
        
        # Create feature importance dictionary
        if aggregate_time:
            feature_importance = {
                self.feature_names[i]: importance[i] 
                for i in range(len(self.feature_names))
            }
        else:
            # For non-aggregated, create time-step specific names
            feature_importance = {}
            seq_len = self.model_wrapper.input_steps
            n_features = len(self.feature_names)
            
            for t in range(seq_len):
                for f in range(n_features):
                    idx = t * n_features + f
                    if idx < len(importance):
                        feature_name = f"{self.feature_names[f]}_t{t}"
                        feature_importance[feature_name] = importance[idx]
        
        return feature_importance

    def plot_feature_importance(self, feature_importance: Dict[str, float] = None,
                               top_k: int = 10, figsize: Tuple[int, int] = (10, 6),
                               save_path: str = None) -> plt.Figure:
        """
        Plot feature importance as a horizontal bar chart.

        Args:
            feature_importance: Feature importance scores
            top_k: Number of top features to show
            figsize: Figure size
            save_path: Path to save the plot

        Returns:
            Matplotlib figure
        """
        if feature_importance is None:
            feature_importance = self.get_feature_importance()

        # Sort features by importance
        sorted_features = sorted(feature_importance.items(),
                               key=lambda x: abs(x[1]), reverse=True)[:top_k]

        features, scores = zip(*sorted_features)

        # Create plot
        fig, ax = plt.subplots(figsize=figsize)
        y_pos = np.arange(len(features))

        bars = ax.barh(y_pos, scores, alpha=0.8)
        ax.set_yticks(y_pos)
        ax.set_yticklabels(features)
        ax.invert_yaxis()
        ax.set_xlabel('SHAP Feature Importance')
        ax.set_title(f'Top {top_k} Feature Importance for Traffic Prediction')

        # Color bars by positive/negative impact
        for i, (bar, score) in enumerate(zip(bars, scores)):
            if score >= 0:
                bar.set_color('steelblue')
            else:
                bar.set_color('coral')

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        return fig

    def plot_shap_summary(self, X_dur: np.ndarray, X_feat: np.ndarray,
                         shap_values: np.ndarray = None, max_display: int = 10,
                         figsize: Tuple[int, int] = (10, 6), save_path: str = None):
        """
        Create SHAP summary plot.

        Args:
            X_dur: Duration data
            X_feat: Feature data
            shap_values: SHAP values
            max_display: Maximum features to display
            figsize: Figure size
            save_path: Path to save the plot
        """
        if shap_values is None:
            shap_values = self.shap_values

        if shap_values is None:
            raise ValueError("SHAP values not available")

        # Prepare feature data for plotting
        X_combined = np.concatenate([X_dur, X_feat], axis=2)
        X_flat = X_combined.reshape(X_combined.shape[0], -1)

        # Aggregate SHAP values and features across time steps
        batch_size = shap_values.shape[0]
        seq_len = self.model_wrapper.input_steps
        n_features = len(self.feature_names)

        shap_reshaped = shap_values.reshape(batch_size, seq_len, n_features)
        X_reshaped = X_flat.reshape(batch_size, seq_len, n_features)

        # Aggregate across time steps
        shap_agg = np.mean(shap_reshaped, axis=1)  # (batch, features)
        X_agg = np.mean(X_reshaped, axis=1)  # (batch, features)

        # Create summary plot
        plt.figure(figsize=figsize)
        shap.summary_plot(shap_agg, X_agg,
                         feature_names=self.feature_names,
                         max_display=max_display, show=False)

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()

    def plot_shap_waterfall(self, sample_idx: int = 0,
                           shap_values: np.ndarray = None,
                           X_dur: np.ndarray = None, X_feat: np.ndarray = None,
                           figsize: Tuple[int, int] = (10, 6), save_path: str = None):
        """
        Create SHAP waterfall plot for a single prediction.

        Args:
            sample_idx: Index of sample to explain
            shap_values: SHAP values
            X_dur: Duration data
            X_feat: Feature data
            figsize: Figure size
            save_path: Path to save the plot
        """
        if shap_values is None:
            shap_values = self.shap_values

        if shap_values is None:
            raise ValueError("SHAP values not available")

        # Aggregate SHAP values across time steps for the sample
        batch_size = shap_values.shape[0]
        seq_len = self.model_wrapper.input_steps
        n_features = len(self.feature_names)

        shap_reshaped = shap_values.reshape(batch_size, seq_len, n_features)
        shap_sample = np.mean(shap_reshaped[sample_idx], axis=0)

        # Get feature values for the sample
        if X_dur is not None and X_feat is not None:
            X_combined = np.concatenate([X_dur, X_feat], axis=2)
            X_reshaped = X_combined.reshape(batch_size, seq_len, n_features)
            X_sample = np.mean(X_reshaped[sample_idx], axis=0)
        else:
            X_sample = None

        # Create waterfall plot
        plt.figure(figsize=figsize)

        # Calculate expected value (baseline)
        expected_value = np.mean(self.model_wrapper.predict(self.background_data))

        # Create explanation object for waterfall plot
        explanation = shap.Explanation(
            values=shap_sample,
            base_values=expected_value,
            data=X_sample,
            feature_names=self.feature_names
        )

        shap.waterfall_plot(explanation, show=False)

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')

        plt.show()


class SHAPAnalysisReport:
    """
    Generate comprehensive SHAP analysis reports for traffic prediction models.
    """

    def __init__(self, explainer: TrafficSHAPExplainer):
        """
        Initialize the report generator.

        Args:
            explainer: Fitted SHAP explainer
        """
        self.explainer = explainer

    def generate_report(self, X_dur: np.ndarray, X_feat: np.ndarray,
                       route_name: str = "Unknown Route",
                       save_dir: str = None) -> Dict[str, Any]:
        """
        Generate a comprehensive SHAP analysis report.

        Args:
            X_dur: Duration data
            X_feat: Feature data
            route_name: Name of the route being analyzed
            save_dir: Directory to save plots

        Returns:
            Dictionary containing analysis results
        """
        print(f"🔍 Generating SHAP analysis report for {route_name}...")

        # Generate SHAP values
        shap_values = self.explainer.explain_prediction(X_dur, X_feat)

        # Calculate feature importance
        feature_importance = self.explainer.get_feature_importance(shap_values)

        # Get top features
        top_features = sorted(feature_importance.items(),
                            key=lambda x: abs(x[1]), reverse=True)[:10]

        # Generate plots if save directory is provided
        plots_generated = []
        if save_dir:
            import os
            os.makedirs(save_dir, exist_ok=True)

            # Feature importance plot
            fig1 = self.explainer.plot_feature_importance(
                feature_importance,
                save_path=f"{save_dir}/feature_importance_{route_name.replace(' ', '_')}.png"
            )
            plots_generated.append("feature_importance")
            plt.close(fig1)

            # Summary plot
            self.explainer.plot_shap_summary(
                X_dur, X_feat, shap_values,
                save_path=f"{save_dir}/summary_plot_{route_name.replace(' ', '_')}.png"
            )
            plots_generated.append("summary_plot")

            # Waterfall plot for first sample
            if X_dur.shape[0] > 0:
                self.explainer.plot_shap_waterfall(
                    0, shap_values, X_dur, X_feat,
                    save_path=f"{save_dir}/waterfall_{route_name.replace(' ', '_')}.png"
                )
                plots_generated.append("waterfall")

        # Create report
        report = {
            'route_name': route_name,
            'n_samples': X_dur.shape[0],
            'n_features': len(self.explainer.feature_names),
            'feature_importance': feature_importance,
            'top_features': top_features,
            'shap_values_shape': shap_values.shape,
            'plots_generated': plots_generated,
            'summary': {
                'most_important_feature': top_features[0][0] if top_features else None,
                'most_important_score': top_features[0][1] if top_features else None,
                'total_absolute_importance': sum(abs(score) for _, score in feature_importance.items())
            }
        }

        print(f"✅ SHAP analysis completed for {route_name}")
        print(f"📊 Most important feature: {report['summary']['most_important_feature']}")
        print(f"📈 Importance score: {report['summary']['most_important_score']:.4f}")

        return report
