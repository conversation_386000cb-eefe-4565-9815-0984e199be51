"""
MLX model definitions for NEXCO traffic prediction.
Contains LSTM models optimized for Apple Silicon NPU/GPU.
"""

import mlx.core as mx
import mlx.nn as nn
import numpy as np
from typing import Tuple, Optional, Dict
from datetime import datetime
import json
import os



class MLXLSTMModel(nn.Module):
    """MLX LSTM model with dual inputs for duration and features."""
    
    def __init__(self, duration_input_size: int = 1, feature_input_size: int = 14, 
                 hidden_size: int = 64, output_size: int = 12):
        super().__init__()
        self.duration_input_size = duration_input_size
        self.feature_input_size = feature_input_size
        self.hidden_size = hidden_size
        
        # Dual-stream architecture
        self.duration_lstm = nn.LSTM(duration_input_size, hidden_size//2, bias=True)
        self.feature_lstm = nn.LSTM(feature_input_size, hidden_size//2, bias=True)
        self.combined_lstm = nn.LSTM(hidden_size, hidden_size//2, bias=True)
        
        # Dense layers with dropout
        self.fc1 = nn.Linear(hidden_size//2, hidden_size//4)
        self.fc2 = nn.Linear(hidden_size//4, output_size)
        self.dropout = nn.Dropout(0.2)
        
    def __call__(self, x_dur, x_feat):
        # Process duration and features separately
        dur_out, _ = self.duration_lstm(x_dur)
        feat_out, _ = self.feature_lstm(x_feat)
        
        # Combine features
        combined = mx.concatenate([dur_out, feat_out], axis=-1)
        combined_out, _ = self.combined_lstm(combined)
        
        # Use last output
        last_output = combined_out[:, -1, :]
        
        # Dense layers with dropout
        x = self.dropout(mx.tanh(self.fc1(last_output)))
        output = self.fc2(x)
        return output


class SequenceGenerator:
    """Utility class for creating enhanced training sequences from time series data."""
    
    @staticmethod
    def create_sequences(duration_data: np.ndarray, feature_data: np.ndarray,
                        input_steps: int = 144, output_steps: int = 12) -> Tuple[Optional[mx.array], Optional[mx.array], Optional[mx.array]]:
        """Create enhanced sequences with multiple features."""
        if len(duration_data) < input_steps + output_steps:
            return None, None, None
        
        X_dur, X_feat, y = [], [], []
        for i in range(len(duration_data) - input_steps - output_steps):
            X_dur.append(duration_data[i:i+input_steps])
            X_feat.append(feature_data[i:i+input_steps])
            y.append(duration_data[i+input_steps:i+input_steps+output_steps])
        
        X_dur = mx.array(np.array(X_dur).reshape(-1, input_steps, 1))
        X_feat = mx.array(np.array(X_feat))  # (batch, seq_len, features)
        y = mx.array(np.array(y))
        return X_dur, X_feat, y


class ModelFactory:
    """Factory class for creating enhanced LSTM models."""
    
    @staticmethod
    def create_model(duration_input_size: int = 1, feature_input_size: int = 8,
                    hidden_size: int = 64, output_size: int = 12) -> MLXLSTMModel:
        """Create a dual-input LSTM model."""
        return MLXLSTMModel(duration_input_size, feature_input_size, 
                           hidden_size, output_size)


class EventImpactLayer:
    """Event Impact Layer for Stage 2 hierarchical predictions."""
    
    def __init__(self, ic_locations_path: str = "/Users/<USER>/dev/nexco-seq/data/constants/ic_locations.json"):
        """Initialize with IC location data for spatial calculations."""
        self.ic_locations = self._load_ic_locations(ic_locations_path)
        
    def _load_ic_locations(self, path: str) -> Dict[str, Tuple[float, float]]:
        """Load IC location coordinates from JSON file."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                locations_data = json.load(f)
            
            # Convert to (lat, lon) format - handle list format
            ic_locations = {}
            if isinstance(locations_data, list):
                for location in locations_data:
                    if 'ic_name' in location and 'lat' in location and 'lng' in location:
                        ic_locations[location['ic_name']] = (location['lat'], location['lng'])
            elif isinstance(locations_data, dict):
                for ic_name, data in locations_data.items():
                    if isinstance(data, dict) and 'latitude' in data and 'longitude' in data:
                        ic_locations[ic_name] = (data['latitude'], data['longitude'])
            
            return ic_locations
        except Exception as e:
            print(f"Warning: Could not load IC locations from {path}: {e}")
            return {}
    
    def calculate_event_impact(self, route_start_ic: str, route_end_ic: str,
                             prediction_time: datetime, events_df=None) -> float:
        """
        Calculate event impact for a route at prediction time.
        
        Args:
            route_start_ic: Starting IC name
            route_end_ic: Ending IC name
            prediction_time: Time for prediction
            events_df: DataFrame with processed events (optional for now)
            
        Returns:
            Event impact factor (0.0 to 5.0)
        """
        # For now, return 0 impact (no events loaded)
        # This will be enhanced when events are integrated
        if events_df is None or len(self.ic_locations) == 0:
            return 0.0
            
        # Use the route event impact calculation from process_road_events.py
        from ..preprocessors.process_road_events import calculate_route_event_impact
        
        return calculate_route_event_impact(
            route_start_ic, route_end_ic, prediction_time, 
            events_df, self.ic_locations
        )


class Stage2HierarchicalModel(nn.Module):
    """Stage 2 Hierarchical model with Event Impact Layer."""
    
    def __init__(self, duration_input_size: int = 1, feature_input_size: int = 14,
                 hidden_size: int = 64, output_size: int = 12):
        super().__init__()
        
        # Base LSTM model (same as Stage 1)
        self.base_model = MLXLSTMModel(
            duration_input_size, feature_input_size, hidden_size, output_size
        )
        
        # Event impact layer
        self.event_layer = EventImpactLayer()
        
    def __call__(self, x_dur, x_feat, route_start_ic: str = None, 
                 route_end_ic: str = None, prediction_time: datetime = None,
                 events_df = None):
        """
        Forward pass with hierarchical prediction.
        
        Args:
            x_dur: Duration sequences
            x_feat: Feature sequences  
            route_start_ic: Starting IC (for event impact)
            route_end_ic: Ending IC (for event impact)
            prediction_time: Prediction timestamp (for event impact)
            events_df: Events DataFrame (optional)
            
        Returns:
            Event-adjusted predictions
        """
        # Base prediction from LSTM model
        base_prediction = self.base_model(x_dur, x_feat)
        
        # Calculate event impact if route info is provided
        if route_start_ic and route_end_ic and prediction_time:
            event_impact = self.event_layer.calculate_event_impact(
                route_start_ic, route_end_ic, prediction_time, events_df
            )
            
            # Apply event impact: final = base * (1 + impact)
            # Convert event_impact to MLX array for computation
            impact_factor = mx.array(1.0 + event_impact)
            final_prediction = base_prediction * impact_factor
            
            return final_prediction
        else:
            # Return base prediction if no event info
            return base_prediction


def loss_fn(model, X_dur, X_feat, y):
    """MSE loss function for enhanced MLX model with dual inputs."""
    predictions = model(X_dur, X_feat)
    return mx.mean((predictions - y) ** 2)

def stage2_loss_fn(model, X_dur, X_feat, y, route_start_ic=None, route_end_ic=None, 
                   prediction_time=None, events_df=None):
    """MSE loss function for Stage 2 hierarchical model with event impact."""
    predictions = model(X_dur, X_feat, route_start_ic, route_end_ic, 
                       prediction_time, events_df)
    return mx.mean((predictions - y) ** 2)