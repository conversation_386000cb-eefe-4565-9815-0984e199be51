"""
Simple data loader for NEXCO traffic analysis.
Loads preprocessed parquet files for ML training.
"""

import os
import pandas as pd
import polars as pl
from sklearn.preprocessing import MinMaxScaler
from typing import Tuple, List


class TrafficDataLoader:
    """Simple loader for preprocessed NEXCO traffic data."""
    
    def __init__(self, data_path: str = "./data/processed/traffic"):
        self.data_path = data_path
        
    def verify_data_availability(self) -> bool:
        """Verify that processed parquet files are available."""
        print("📊 Verifying processed traffic data availability...")
        
        if not os.path.exists(self.data_path):
            print(f"❌ Processed data directory not found: {self.data_path}")
            print(f"💡 Please run: python preprocessors/process_traffic.py")
            return False
            
        parquet_files = [f for f in os.listdir(self.data_path) if f.endswith('.parquet')]
        if not parquet_files:
            print(f"❌ No processed parquet files found in {self.data_path}")
            print(f"💡 Please run: python preprocessors/process_traffic.py")
            return False
            
        total_size = sum(os.path.getsize(os.path.join(self.data_path, f)) for f in parquet_files) / (1024*1024)
        print(f"✅ Found {len(parquet_files)} processed files ({total_size:.1f} MB total)")
        for pf in sorted(parquet_files):
            size = os.path.getsize(os.path.join(self.data_path, pf)) / (1024*1024)
            print(f"  • {pf} ({size:.1f} MB)")
            
        return True
    
    def load_data(self, months_to_load: List[str]) -> pd.DataFrame:
        """Load processed traffic data from parquet files."""
        print(f"📊 Loading processed data for {len(months_to_load)} months...")
        
        dataframes = []
        for month in months_to_load:
            parquet_file = os.path.join(self.data_path, f"{month}.parquet")
            if os.path.exists(parquet_file):
                print(f"📁 Loading {month}...")
                df_month = pl.read_parquet(parquet_file)
                dataframes.append(df_month)
                print(f"   ✅ Loaded {df_month.shape[0]:,} rows × {df_month.shape[1]} columns")
            else:
                print(f"   ❌ File not found: {parquet_file}")
        
        if not dataframes:
            raise ValueError("❌ No data files found! Make sure the preprocessing step completed successfully.")
        
        # Combine all months
        print(f"🔗 Combining {len(dataframes)} months of data...")
        df_pl = pl.concat(dataframes)
        
        # Convert to pandas for ML compatibility
        df = df_pl.to_pandas()
        
        # Basic data validation
        df["record_time"] = pd.to_datetime(df["record_time"])
        df = df.sort_values("record_time")
        df = df.dropna(subset=['duration_seconds'])
        
        print(f"📊 Combined dataset: {df.shape[0]:,} rows × {df.shape[1]} columns")
        print(f"📅 Date range: {df['record_time'].min()} to {df['record_time'].max()}")
        
        # Print route summary
        route_groups = df.groupby(["ic_name_start", "ic_name_end"])
        print(f"📍 Found {len(route_groups):,} unique routes")
        
        # Check available ML features (extracted during preprocessing)
        ml_features = self.get_ml_feature_columns()
        available_features = [col for col in ml_features if col in df.columns]
        print(f"🎯 Available ML features: {len(available_features)}/{len(ml_features)}")
        for feat in available_features:
            unique_vals = df[feat].nunique()
            print(f"   • {feat}: {unique_vals} unique values")
        
        return df
    
    def get_ml_feature_columns(self) -> List[str]:
        """Get list of ML feature column names (14 enhanced features with weather integration)."""
        return [
            # Temporal features (5 features)
            'hour', 'day_of_week', 'is_weekend', 'is_rush_hour', 'is_holiday',
            # Weather features (8 features - 2 existing + 6 new)
            'has_precipitation', 'max_precip',  # Existing weather features
            'temp_start', 'temp_end',           # New temperature features  
            'precip_start', 'precip_end',       # New precipitation features
            'wind_speed_start', 'wind_speed_end', # New wind features
            # Infrastructure features (1 feature)
            'speed_profile_numeric'
        ]
    
    def create_scalers(self, group_data: pd.DataFrame) -> Tuple[MinMaxScaler, MinMaxScaler]:
        """Create and fit scalers for duration and features."""
        # Duration scaler
        scaler_duration = MinMaxScaler()
        scaler_duration.fit(group_data[["duration_seconds"]])
        
        # Feature scaler
        ml_features = self.get_ml_feature_columns()
        available_features = [col for col in ml_features if col in group_data.columns]
        
        scaler_features = MinMaxScaler()
        scaler_features.fit(group_data[available_features])
        
        return scaler_duration, scaler_features