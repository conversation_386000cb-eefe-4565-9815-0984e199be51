"""
Configuration settings for NEXCO traffic prediction system.
"""

from dataclasses import dataclass
from typing import List, Dict, Any


@dataclass
class ModelConfig:
    """Model architecture configuration."""
    # LSTM Model Parameters
    hidden_size: int = 64
    dropout_rate: float = 0.2
    
    # Input/Output Parameters
    input_steps: int = 144
    output_steps: int = 12
    duration_input_size: int = 1
    feature_input_size: int = 8


@dataclass
class TrainingConfig:
    """Training configuration."""
    batch_size: int = 32
    epochs: int = 50
    learning_rate: float = 0.001
    patience: int = 10
    validation_split: float = 0.15
    test_split: float = 0.15
    
    # MLflow Configuration
    experiment_name: str = "NEXCO_Traffic_Enhanced_AppleSilicon"
    tracking_uri: str = None  # None uses local tracking


@dataclass
class DataConfig:
    """Data processing configuration."""
    # Data Paths - Points to processed data directory
    output_path: str = "./data/processed/traffic"
    
    # Processing Parameters
    months_limit: int = 2
    batch_size_processing: int = 20
    max_files_per_month: int = 100
    
    # Data Loading
    months_to_load: List[str] = None
    
    def __post_init__(self):
        if self.months_to_load is None:
            self.months_to_load = ["traffic_202405", "traffic_202406"]


@dataclass
class FeatureConfig:
    """Feature engineering configuration."""
    # Enhanced Features
    enhanced_features: List[str] = None
    
    # Temporal Features
    use_holiday_features: bool = True
    use_weekend_features: bool = True
    use_rush_hour_features: bool = True
    
    # Weather Features
    use_weather_features: bool = True
    
    # Infrastructure Features
    use_infrastructure_features: bool = True
    
    def __post_init__(self):
        if self.enhanced_features is None:
            self.enhanced_features = [
                'hour', 'day_of_week', 'is_weekend', 'is_rush_hour', 
                'is_holiday', 'has_precipitation', 'max_precip', 
                'speed_profile_numeric'
            ]


@dataclass
class PredictionConfig:
    """Prediction configuration."""
    # Confidence Prediction
    num_confidence_samples: int = 10
    confidence_level: float = 0.95
    
    # Route Selection
    max_routes_training: int = 2
    min_data_points: int = 300
    min_sequences: int = 100


@dataclass
class SystemConfig:
    """System and performance configuration."""
    # Apple Silicon Optimization
    use_mlx: bool = True
    use_apple_silicon_optimization: bool = True
    
    # Logging
    log_level: str = "INFO"
    verbose: bool = True
    
    # Performance
    enable_parallel_processing: bool = True
    max_workers: int = 4


class Config:
    """Main configuration class that combines all config sections."""
    
    def __init__(self):
        self.model = ModelConfig()
        self.training = TrainingConfig()
        self.data = DataConfig()
        self.features = FeatureConfig()
        self.prediction = PredictionConfig()
        self.system = SystemConfig()
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'Config':
        """Create config from dictionary."""
        config = cls()
        
        if 'model' in config_dict:
            for key, value in config_dict['model'].items():
                if hasattr(config.model, key):
                    setattr(config.model, key, value)
        
        if 'training' in config_dict:
            for key, value in config_dict['training'].items():
                if hasattr(config.training, key):
                    setattr(config.training, key, value)
        
        if 'data' in config_dict:
            for key, value in config_dict['data'].items():
                if hasattr(config.data, key):
                    setattr(config.data, key, value)
        
        if 'features' in config_dict:
            for key, value in config_dict['features'].items():
                if hasattr(config.features, key):
                    setattr(config.features, key, value)
        
        if 'prediction' in config_dict:
            for key, value in config_dict['prediction'].items():
                if hasattr(config.prediction, key):
                    setattr(config.prediction, key, value)
        
        if 'system' in config_dict:
            for key, value in config_dict['system'].items():
                if hasattr(config.system, key):
                    setattr(config.system, key, value)
        
        return config
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            'model': self.model.__dict__,
            'training': self.training.__dict__,
            'data': self.data.__dict__,
            'features': self.features.__dict__,
            'prediction': self.prediction.__dict__,
            'system': self.system.__dict__
        }
    
    def get_training_config_dict(self) -> Dict[str, Any]:
        """Get configuration dictionary suitable for trainer."""
        return {
            'batch_size': self.training.batch_size,
            'epochs': self.training.epochs,
            'learning_rate': self.training.learning_rate,
            'patience': self.training.patience,
            'input_steps': self.model.input_steps,
            'output_steps': self.model.output_steps,
            'hidden_size': self.model.hidden_size,
            'validation_split': self.training.validation_split,
            'test_split': self.training.test_split
        }


# Default configuration instance
DEFAULT_CONFIG = Config()

# Configuration presets for different use cases
QUICK_TEST_CONFIG = Config()
QUICK_TEST_CONFIG.data.months_limit = 1
QUICK_TEST_CONFIG.prediction.max_routes_training = 1
QUICK_TEST_CONFIG.training.epochs = 10

PRODUCTION_CONFIG = Config()
PRODUCTION_CONFIG.data.months_limit = 12
PRODUCTION_CONFIG.prediction.max_routes_training = 50
PRODUCTION_CONFIG.training.epochs = 100
PRODUCTION_CONFIG.training.patience = 15