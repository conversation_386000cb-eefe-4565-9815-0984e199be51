"""
Training module for NEXCO traffic prediction models.
Handles model training, validation, and MLflow experiment tracking.
"""

import mlx.core as mx
import mlx.nn as nn
import mlx.optimizers as optim
import numpy as np
import pandas as pd
import mlflow
import mlflow.sklearn
from sklearn.metrics import mean_squared_error
from typing import Dict, Tuple, Any, Optional
import pickle
import tempfile
import os
import psutil
import time

from .models import MLXLSTMModel, SequenceGenerator, loss_fn
from .data_loader import TrafficDataLoader


class Trainer:
    """Trainer for enhanced MLX LSTM models with MLflow tracking."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.batch_size = config.get('batch_size', 32)
        self.epochs = config.get('epochs', 50)
        self.learning_rate = config.get('learning_rate', 0.001)
        self.patience = config.get('patience', 10)
        self.input_steps = config.get('input_steps', 144)
        self.output_steps = config.get('output_steps', 12)
        self.hidden_size = config.get('hidden_size', 64)
        
        self.data_loader = TrafficDataLoader()
        self.models = {}
        
    def setup_mlflow_experiment(self, experiment_name: str = "NEXCO_Traffic_Enhanced_AppleSilicon"):
        """Setup MLflow experiment for tracking with autologging and system metrics."""
        mlflow.set_experiment(experiment_name)
        
        # Enable autologging for scikit-learn (for scalers)
        mlflow.sklearn.autolog(log_input_examples=True, log_model_signatures=True)
        
        # Enable system metrics logging
        mlflow.enable_system_metrics_logging()
        
        print("✅ MLflow autologging enabled for sklearn components")
        print("🔧 Enhanced manual logging enabled for MLX models")
        print("📊 System metrics logging enabled (CPU, RAM, GPU)")
        
    def setup_system_metrics_logging(self):
        """Setup detailed system metrics logging."""
        try:
            # Enable system metrics with custom sampling interval
            mlflow.enable_system_metrics_logging()
            print("✅ System metrics enabled - MLflow will automatically track:")
            print("   • CPU usage and load")
            print("   • RAM usage and availability") 
            print("   • GPU utilization (if available)")
            print("   • Disk I/O")
            print("   • Network I/O")
        except Exception as e:
            print(f"⚠️ Could not enable system metrics: {e}")
            print("💡 System metrics require mlflow>=2.8.0")
        
    def get_memory_usage(self):
        """Get current memory usage statistics."""
        try:
            # System RAM
            process = psutil.Process(os.getpid())
            ram_info = process.memory_info()
            
            # MLX GPU memory (if available)
            try:
                gpu_info = mx.metal.get_memory_info()
                gpu_memory = {
                    'gpu_used_mb': gpu_info.get('current', 0) / (1024**2),
                    'gpu_peak_mb': gpu_info.get('peak', 0) / (1024**2),
                    'gpu_cache_mb': gpu_info.get('cache', 0) / (1024**2)
                }
            except:
                gpu_memory = {'gpu_used_mb': 0, 'gpu_peak_mb': 0, 'gpu_cache_mb': 0}
            
            return {
                'ram_used_mb': ram_info.rss / (1024**2),
                'ram_percent': process.memory_percent(),
                'system_ram_total_gb': psutil.virtual_memory().total / (1024**3),
                **gpu_memory
            }
        except Exception as e:
            return {'error': str(e)}
    
    def train_step(self, model, optimizer, X_dur_batch, X_feat_batch, y_batch):
        """Single training step optimized for Apple Silicon."""
        loss_and_grad_fn = nn.value_and_grad(model, loss_fn)
        loss, grads = loss_and_grad_fn(model, X_dur_batch, X_feat_batch, y_batch)
        optimizer.update(model, grads)
        return loss
        
    def train_route_model(self, route_start: str, route_end: str, 
                         group_data: pd.DataFrame, route_count: int) -> Optional[Tuple]:
        """Train model for a specific route."""
        
        with mlflow.start_run(run_name=f"Enhanced_Route_{route_count}_{route_start}_to_{route_end}"):
            print(f"\n🚗 Processing Enhanced Route {route_count}: {route_start} to {route_end}, rows: {len(group_data)}")
            
            # Log route metadata
            self._log_route_metadata(route_start, route_end, group_data)
            
            if len(group_data) < 300:
                print("Skipping - too few rows")
                mlflow.log_param("status", "skipped_insufficient_data")
                return None
                
            try:
                return self._train_single_route(group_data, route_start, route_end)
                
            except Exception as e:
                print(f"❌ Error processing route {route_start} to {route_end}: {str(e)}")
                mlflow.log_param("status", "failed")
                mlflow.log_param("error_message", str(e))
                return None
    
    def _log_route_metadata(self, route_start: str, route_end: str, group_data: pd.DataFrame):
        """Log route metadata to MLflow."""
        mlflow.log_param("route_start", route_start)
        mlflow.log_param("route_end", route_end)
        mlflow.log_param("data_points", len(group_data))
        mlflow.log_param("batch_size", self.batch_size)
        mlflow.log_param("epochs", self.epochs)
        mlflow.log_param("learning_rate", self.learning_rate)
        mlflow.log_param("model_type", "MLX_LSTM_Enhanced")
        mlflow.log_param("input_steps", self.input_steps)
        mlflow.log_param("output_steps", self.output_steps)
        mlflow.log_param("enhanced_features", "weather+temporal+infrastructure")
        
    def _train_single_route(self, group_data: pd.DataFrame, route_start: str, 
                           route_end: str) -> Tuple:
        """Train model for a single route."""
        group_data = group_data.copy()
        
        # Create scalers and scale data
        scaler_duration, scaler_features = self.data_loader.create_scalers(group_data)
        duration_scaled = scaler_duration.transform(group_data[["duration_seconds"]]).flatten()
        
        enhanced_features = self.data_loader.get_ml_feature_columns()
        available_features = [col for col in enhanced_features if col in group_data.columns]
        features_scaled = scaler_features.transform(group_data[available_features])
        
        # Log data statistics
        self._log_data_statistics(group_data, available_features)
        
        # Create sequences
        X_dur, X_feat, y = SequenceGenerator.create_sequences(
            duration_scaled, features_scaled, self.input_steps, self.output_steps
        )
        
        if X_dur is None:
            print("No sequences created")
            mlflow.log_param("status", "failed_sequence_creation")
            return None
            
        print(f"X_dur shape: {X_dur.shape}, X_feat shape: {X_feat.shape}, y shape: {y.shape}")
        mlflow.log_param("sequence_count", X_dur.shape[0])
        mlflow.log_param("feature_count", X_feat.shape[2])
        
        if X_dur.shape[0] < 100:
            print("Insufficient data for sequences")
            mlflow.log_param("status", "insufficient_sequences")
            return None
            
        # Split data
        X_dur_train, X_feat_train, y_train, X_dur_val, X_feat_val, y_val, X_dur_test, X_feat_test, y_test = self._split_data(
            X_dur, X_feat, y
        )
        
        # Create and train model
        model = MLXLSTMModel(
            duration_input_size=1,
            feature_input_size=X_feat.shape[2],
            hidden_size=self.hidden_size,
            output_size=self.output_steps
        )
        
        optimizer = optim.Adam(learning_rate=self.learning_rate)
        
        # Log model parameters
        self._log_model_parameters(X_feat.shape[2])
        
        # Estimate and log memory requirements (for planning purposes)
        self._log_memory_estimates(X_dur_train.shape[0], X_feat.shape[2])
        
        # Training loop
        best_model_state = self._training_loop(model, optimizer, 
                                             X_dur_train, X_feat_train, y_train,
                                             X_dur_val, X_feat_val, y_val)
        
        # Enhanced logging (autolog-style) for model artifacts
        self._log_model_artifacts(model, scaler_duration, scaler_features, 
                                 route_start, route_end)
        
        # System metrics will handle all resource monitoring automatically
        
        # Evaluation
        self._evaluate_model(model, scaler_duration, 
                           X_dur_test, X_feat_test, y_test,
                           route_start, route_end, len(available_features))
        
        return (model, scaler_duration, scaler_features)
    
    def _log_data_statistics(self, group_data: pd.DataFrame, available_features: list):
        """Log data statistics to MLflow."""
        mlflow.log_metric("data_mean_duration", float(np.mean(group_data["duration_seconds"])))
        mlflow.log_metric("data_std_duration", float(np.std(group_data["duration_seconds"])))
        mlflow.log_metric("data_min_duration", float(np.min(group_data["duration_seconds"])))
        mlflow.log_metric("data_max_duration", float(np.max(group_data["duration_seconds"])))
        
        mlflow.log_param("num_enhanced_features", len(available_features))
        mlflow.log_param("enhanced_feature_list", str(available_features))
        
        if 'has_precipitation' in group_data.columns:
            precip_rate = group_data['has_precipitation'].mean()
            mlflow.log_metric("precipitation_rate", float(precip_rate))
        
        if 'is_holiday' in group_data.columns:
            holiday_rate = group_data['is_holiday'].mean()
            mlflow.log_metric("holiday_rate", float(holiday_rate))
    
    def _split_data(self, X_dur, X_feat, y) -> Tuple:
        """Split data into train, validation, and test sets."""
        total_samples = X_dur.shape[0]
        train_size = int(0.7 * total_samples)
        val_size = int(0.15 * total_samples)
        
        X_dur_train, X_feat_train, y_train = X_dur[:train_size], X_feat[:train_size], y[:train_size]
        X_dur_val, X_feat_val, y_val = X_dur[train_size:train_size+val_size], X_feat[train_size:train_size+val_size], y[train_size:train_size+val_size]
        X_dur_test, X_feat_test, y_test = X_dur[train_size+val_size:], X_feat[train_size+val_size:], y[train_size+val_size:]
        
        print(f"Training: {X_dur_train.shape}, Validation: {X_dur_val.shape}, Test: {X_dur_test.shape}")
        
        # Log data split info
        mlflow.log_param("train_samples", X_dur_train.shape[0])
        mlflow.log_param("val_samples", X_dur_val.shape[0])
        mlflow.log_param("test_samples", X_dur_test.shape[0])
        
        return X_dur_train, X_feat_train, y_train, X_dur_val, X_feat_val, y_val, X_dur_test, X_feat_test, y_test
    
    def _log_model_parameters(self, feature_input_size: int):
        """Log model parameters to MLflow."""
        mlflow.log_param("model_architecture", "dual_stream_lstm")
        mlflow.log_param("hidden_size", self.hidden_size)
        mlflow.log_param("duration_input_size", 1)
        mlflow.log_param("feature_input_size", feature_input_size)
    
    def _log_memory_estimates(self, batch_size: int, features: int):
        """Log estimated memory requirements to MLflow."""
        try:
            # MLX uses float32 (4 bytes per parameter)
            bytes_per_param = 4
            
            # Input data memory
            input_duration_mb = batch_size * self.input_steps * 1 * bytes_per_param / (1024**2)
            input_features_mb = batch_size * self.input_steps * features * bytes_per_param / (1024**2)
            
            # Model parameters (rough estimates for dual-stream LSTM)
            duration_lstm_params = 1 * (self.hidden_size // 2) * 4 * 2
            feature_lstm_params = features * (self.hidden_size // 2) * 4 * 2  
            combined_lstm_params = self.hidden_size * (self.hidden_size // 2) * 4 * 2
            fc_params = (self.hidden_size // 2) * (self.hidden_size // 4) + (self.hidden_size // 4) * self.output_steps
            
            total_model_params = duration_lstm_params + feature_lstm_params + combined_lstm_params + fc_params
            model_memory_mb = total_model_params * bytes_per_param / (1024**2)
            
            # Gradient memory (same as model parameters)
            gradient_memory_mb = model_memory_mb
            
            # Activation memory (rough estimate)
            activation_memory_mb = batch_size * self.input_steps * self.hidden_size * bytes_per_param / (1024**2)
            
            # Total estimates with overhead
            total_training_mb = (input_duration_mb + input_features_mb + 
                                model_memory_mb + gradient_memory_mb + 
                                activation_memory_mb) * 1.5  # 50% overhead
            
            # Log estimates as parameters (not metrics) since they're configuration-based
            mlflow.log_param("memory_estimate_input_data_mb", f"{input_duration_mb + input_features_mb:.1f}")
            mlflow.log_param("memory_estimate_model_params_mb", f"{model_memory_mb:.1f}")
            mlflow.log_param("memory_estimate_total_mb", f"{total_training_mb:.1f}")
            mlflow.log_param("memory_estimate_recommended_ram_gb", f"{max(8, total_training_mb / 1024 * 2):.1f}")
            
            # Log parameters used for estimation
            mlflow.log_param("memory_estimate_batch_size", batch_size)
            mlflow.log_param("memory_estimate_sequence_length", self.input_steps)
            mlflow.log_param("memory_estimate_features", features)
            
            print(f"📊 Memory estimates logged: {total_training_mb:.1f}MB total estimated")
            
        except Exception as e:
            print(f"⚠️ Could not estimate memory requirements: {e}")
    
    def _training_loop(self, model, optimizer, X_dur_train, X_feat_train, y_train,
                      X_dur_val, X_feat_val, y_val) -> Dict:
        """Main training loop with early stopping and MLflow logging."""
        best_val_loss = float('inf')
        patience_counter = 0
        
        print("🔥 Training model with Apple Silicon acceleration + MLflow tracking...")
        
        # Track training start time and memory
        training_start_time = time.time()
        
        for epoch in range(self.epochs):
            epoch_loss = 0
            num_batches = 0
            
            # Training batches
            for i in range(0, X_dur_train.shape[0], self.batch_size):
                end_idx = min(i + self.batch_size, X_dur_train.shape[0])
                X_dur_batch = X_dur_train[i:end_idx]
                X_feat_batch = X_feat_train[i:end_idx]
                y_batch = y_train[i:end_idx]
                
                loss = self.train_step(model, optimizer, X_dur_batch, X_feat_batch, y_batch)
                epoch_loss += loss.item()
                num_batches += 1
            
            avg_loss = epoch_loss / num_batches
            val_loss = loss_fn(model, X_dur_val, X_feat_val, y_val).item()
            
            # Enhanced autolog-style metrics
            mlflow.log_metric("train_loss", avg_loss, step=epoch)
            mlflow.log_metric("val_loss", val_loss, step=epoch)
            
            # Additional autolog-style metrics
            mlflow.log_metric("learning_rate", self.learning_rate, step=epoch)
            mlflow.log_metric("epoch", epoch, step=epoch)
            mlflow.log_metric("num_batches", num_batches, step=epoch)
            
            # Log improvement rate
            if epoch > 0:
                improvement = (best_val_loss - val_loss) / best_val_loss * 100
                mlflow.log_metric("val_loss_improvement_pct", improvement, step=epoch)
            
            if epoch % 10 == 0 or epoch < 5:
                print(f"Epoch {epoch}: Train Loss: {avg_loss:.6f}, Val Loss: {val_loss:.6f}")
                # System metrics will automatically track all resource usage
            
            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                mlflow.log_metric("best_val_loss", best_val_loss, step=epoch)
            else:
                patience_counter += 1
                if patience_counter >= self.patience:
                    print(f"Early stopping at epoch {epoch}")
                    mlflow.log_param("early_stop_epoch", epoch)
                    break
        
        # Log final training duration (system metrics will handle detailed resource usage)
        training_duration = time.time() - training_start_time
        mlflow.log_metric("training_duration_minutes", training_duration / 60)
        
        return {"best_val_loss": best_val_loss}
    
    def _log_model_artifacts(self, model, scaler_duration, scaler_features, 
                           route_start: str, route_end: str):
        """Log complete model with MLX weights for sharing and deployment."""
        try:
            import tempfile
            import pickle
            import mlx.core as mx
            import numpy as np
            from mlflow.pyfunc import PythonModel
            
            # Create a wrapper class for MLX model
            class MLXTrafficPredictor(PythonModel):
                def load_context(self, context):
                    """Load complete model with weights."""
                    import pickle
                    import mlx.core as mx
                    import numpy as np
                    from sequence.models import MLXLSTMModel
                    
                    # Load scalers
                    with open(context.artifacts["duration_scaler"], "rb") as f:
                        self.duration_scaler = pickle.load(f)
                    with open(context.artifacts["feature_scaler"], "rb") as f:
                        self.feature_scaler = pickle.load(f)
                    
                    # Load model info
                    with open(context.artifacts["model_info"], "rb") as f:
                        model_info = pickle.load(f)
                    
                    # Recreate the MLX model
                    self.model = MLXLSTMModel(
                        duration_input_size=model_info['architecture']['duration_input_size'],
                        feature_input_size=model_info['architecture']['feature_input_size'],
                        hidden_size=model_info['architecture']['hidden_size']
                    )
                    
                    # Load model weights
                    with open(context.artifacts["model_weights"], "rb") as f:
                        weights_data = pickle.load(f)
                    
                    # Restore MLX model weights
                    self._restore_mlx_weights(weights_data)
                
                def _restore_mlx_weights(self, weights_data):
                    """Restore MLX model weights from serialized data."""
                    # Duration LSTM weights
                    if 'duration_lstm' in weights_data:
                        for key, value in weights_data['duration_lstm'].items():
                            setattr(self.model.duration_lstm, key, mx.array(value))
                    
                    # Feature LSTM weights  
                    if 'feature_lstm' in weights_data:
                        for key, value in weights_data['feature_lstm'].items():
                            setattr(self.model.feature_lstm, key, mx.array(value))
                    
                    # Combined LSTM weights
                    if 'combined_lstm' in weights_data:
                        for key, value in weights_data['combined_lstm'].items():
                            setattr(self.model.combined_lstm, key, mx.array(value))
                    
                    # Dense layer weights
                    if 'fc1' in weights_data:
                        self.model.fc1.weight = mx.array(weights_data['fc1']['weight'])
                        if 'bias' in weights_data['fc1']:
                            self.model.fc1.bias = mx.array(weights_data['fc1']['bias'])
                    
                    if 'fc2' in weights_data:
                        self.model.fc2.weight = mx.array(weights_data['fc2']['weight'])
                        if 'bias' in weights_data['fc2']:
                            self.model.fc2.bias = mx.array(weights_data['fc2']['bias'])
                
                def predict(self, context, model_input):
                    """Make predictions using the loaded MLX model."""
                    # model_input should be dict with 'duration' and 'features' keys
                    duration_data = mx.array(model_input['duration'])
                    feature_data = mx.array(model_input['features'])
                    
                    # Make prediction
                    predictions = self.model(duration_data, feature_data)
                    return np.array(predictions)
            
            # Create temporary directory for artifacts
            with tempfile.TemporaryDirectory() as temp_dir:
                
                # Save model info
                model_info_path = os.path.join(temp_dir, "model_info.pkl")
                with open(model_info_path, 'wb') as f:
                    model_info = {
                        'model_type': 'MLXLSTMModel',
                        'architecture': {
                            'duration_input_size': model.duration_input_size,
                            'feature_input_size': model.feature_input_size, 
                            'hidden_size': model.hidden_size
                        },
                        'route_info': {
                            'start_ic': route_start,
                            'end_ic': route_end
                        }
                    }
                    pickle.dump(model_info, f)
                
                # Save MLX model weights (convert to numpy for serialization)
                model_weights_path = os.path.join(temp_dir, "model_weights.pkl")
                with open(model_weights_path, 'wb') as f:
                    weights_data = {
                        'duration_lstm': {
                            'Wih': np.array(model.duration_lstm.Wih) if hasattr(model.duration_lstm, 'Wih') else None,
                            'Whh': np.array(model.duration_lstm.Whh) if hasattr(model.duration_lstm, 'Whh') else None,
                            'bias': np.array(model.duration_lstm.bias) if hasattr(model.duration_lstm, 'bias') else None,
                        },
                        'feature_lstm': {
                            'Wih': np.array(model.feature_lstm.Wih) if hasattr(model.feature_lstm, 'Wih') else None,
                            'Whh': np.array(model.feature_lstm.Whh) if hasattr(model.feature_lstm, 'Whh') else None,
                            'bias': np.array(model.feature_lstm.bias) if hasattr(model.feature_lstm, 'bias') else None,
                        },
                        'combined_lstm': {
                            'Wih': np.array(model.combined_lstm.Wih) if hasattr(model.combined_lstm, 'Wih') else None,
                            'Whh': np.array(model.combined_lstm.Whh) if hasattr(model.combined_lstm, 'Whh') else None,
                            'bias': np.array(model.combined_lstm.bias) if hasattr(model.combined_lstm, 'bias') else None,
                        },
                        'fc1': {
                            'weight': np.array(model.fc1.weight),
                            'bias': np.array(model.fc1.bias) if hasattr(model.fc1, 'bias') and model.fc1.bias is not None else None,
                        },
                        'fc2': {
                            'weight': np.array(model.fc2.weight),
                            'bias': np.array(model.fc2.bias) if hasattr(model.fc2, 'bias') and model.fc2.bias is not None else None,
                        }
                    }
                    pickle.dump(weights_data, f)
                
                # Save scalers
                duration_scaler_path = os.path.join(temp_dir, "duration_scaler.pkl")
                feature_scaler_path = os.path.join(temp_dir, "feature_scaler.pkl")
                
                with open(duration_scaler_path, 'wb') as f:
                    pickle.dump(scaler_duration, f)
                with open(feature_scaler_path, 'wb') as f:
                    pickle.dump(scaler_features, f)
                
                # Create artifacts dictionary
                artifacts = {
                    "model_info": model_info_path,
                    "model_weights": model_weights_path,
                    "duration_scaler": duration_scaler_path,
                    "feature_scaler": feature_scaler_path
                }
                
                # Create conda environment specification
                conda_env = {
                    "name": "nexco_mlx_env",
                    "channels": ["conda-forge"],
                    "dependencies": [
                        "python=3.10",
                        "pip",
                        {
                            "pip": [
                                "mlx>=0.0.6",
                                "numpy",
                                "scikit-learn",
                                "polars",
                                "mlflow"
                            ]
                        }
                    ]
                }
                
                # Model name for registry
                model_name = f"nexco_traffic_{route_start}_to_{route_end}".replace(" ", "_").replace("ＩＣ", "IC")
                
                # Log complete model using MLflow Models format
                mlflow.pyfunc.log_model(
                    artifact_path="model",
                    python_model=MLXTrafficPredictor(),
                    artifacts=artifacts,
                    conda_env=conda_env,
                    registered_model_name=model_name
                )
                
                print(f"✅ Complete MLX model (with weights) logged and registered as: {model_name}")
                print(f"📊 Model format: MLflow Models (pyfunc) - Shareable & Deployable")
                print(f"🔗 Model can be shared via mlruns/ directory or MLflow Model Registry")
                
        except Exception as e:
            print(f"⚠️ Could not log complete MLflow Model: {e}")
            print("📝 Falling back to basic artifact logging...")
            
            # Fallback to simple artifact logging
            try:
                with tempfile.TemporaryDirectory() as temp_dir:
                    model_path = os.path.join(temp_dir, "mlx_model.pkl")
                    with open(model_path, 'wb') as f:
                        model_info = {
                            'model_type': 'MLXLSTMModel',
                            'architecture': {
                                'duration_input_size': model.duration_input_size,
                                'feature_input_size': model.feature_input_size, 
                                'hidden_size': model.hidden_size
                            },
                            'route_info': {
                                'start_ic': route_start,
                                'end_ic': route_end
                            }
                        }
                        pickle.dump(model_info, f)
                    
                    mlflow.log_artifacts(temp_dir, "model_artifacts")
                    print("✅ Basic model artifacts logged")
                    
            except Exception as fallback_error:
                print(f"❌ Could not log any model artifacts: {fallback_error}")
    
    def _evaluate_model(self, model, scaler_duration, X_dur_test, X_feat_test, y_test,
                       route_start: str, route_end: str, num_features: int):
        """Evaluate trained model and log metrics."""
        y_pred = model(X_dur_test, X_feat_test)
        y_pred_np = np.array(y_pred)
        y_test_np = np.array(y_test)
        
        # Rescale predictions
        y_pred_rescaled = scaler_duration.inverse_transform(y_pred_np.reshape(-1, 1)).reshape(-1, self.output_steps)
        y_test_rescaled = scaler_duration.inverse_transform(y_test_np.reshape(-1, 1)).reshape(-1, self.output_steps)
        
        rmse = np.sqrt(mean_squared_error(y_test_rescaled, y_pred_rescaled))
        mae = np.mean(np.abs(y_test_rescaled - y_pred_rescaled))
        relative_error = (rmse/np.mean(y_test_rescaled)*100)
        
        # Log final metrics to MLflow
        mlflow.log_metric("final_rmse_seconds", rmse)
        mlflow.log_metric("final_rmse_minutes", rmse/60)
        mlflow.log_metric("final_mae_seconds", mae)
        mlflow.log_metric("final_mae_minutes", mae/60)
        mlflow.log_metric("relative_error_percent", relative_error)
        mlflow.log_param("status", "completed_successfully")
        
        # Calculate efficiency metrics (system metrics will provide detailed resource data)
        try:
            current_run = mlflow.active_run()
            if current_run:
                training_duration = current_run.data.metrics.get('training_duration_minutes', 0)
                total_samples = current_run.data.params.get('train_samples', 0)
                
                if training_duration > 0 and total_samples:
                    if isinstance(total_samples, str):
                        total_samples = int(total_samples)
                    
                    # Training speed efficiency
                    samples_per_minute = total_samples / training_duration
                    mlflow.log_metric("training_samples_per_minute", samples_per_minute)
                    
                    # Accuracy per training time
                    accuracy_per_minute = (100 - relative_error) / training_duration
                    mlflow.log_metric("accuracy_per_training_minute", accuracy_per_minute)
                    
        except Exception as e:
            print(f"⚠️ Could not calculate efficiency metrics: {e}")
        
        print(f"🎯 RESULTS for {route_start} to {route_end}:")
        print(f"  📊 RMSE: {rmse:.1f} seconds ({rmse/60:.1f} minutes)")
        print(f"  📈 MAE: {mae:.1f} seconds ({mae/60:.1f} minutes)")
        print(f"  📏 Relative Error: {relative_error:.1f}%")
        print(f"  🚀 Features used: {num_features} enhanced features")
        
        print(f"  📊 System metrics: Check MLflow UI for detailed resource usage")
    
    def train_multiple_routes(self, df: pd.DataFrame, max_routes: int = 2) -> Dict:
        """Train models for multiple routes."""
        print("🚀 Training models with Apple Silicon NPU/GPU + MLflow tracking...")
        print("   • Using weather data (precipitation)")
        print("   • Using temporal features (holidays, weekdays, rush hours)")
        print("   • Using infrastructure data (speed profiles)")
        
        self.setup_mlflow_experiment()
        
        route_groups = df.groupby(["ic_name_start", "ic_name_end"])
        route_count = 0
        
        for (start, end), group in route_groups:
            route_count += 1
            if route_count > max_routes:
                print(f"Stopping after {max_routes} routes for testing...")
                break
            
            result = self.train_route_model(start, end, group, route_count)
            if result is not None:
                self.models[f"{start}_to_{end}"] = result
        
        print(f"\n🎉 Completed training {len(self.models)} models with Apple Silicon acceleration!")
        print("📊 View results in MLflow UI: run 'mlflow ui' in terminal, then open http://localhost:5000")
        print("🆕 Enhanced features include: weather, holidays, rush hours, speed profiles, and more!")
        
        return self.models