# NEXCO Traffic Prediction System

A modular Python system for processing NEXCO traffic data and training LSTM models optimized for Apple Silicon using the MLX framework.

## Features

- **Apple Silicon Optimized**: Uses MLX framework for NPU/GPU acceleration on M1/M2/M3 chips
- **Enhanced Features**: Incorporates weather, temporal, and infrastructure data
- **SHAP Explainability**: Model interpretability with feature importance analysis and visualizations
- **Modular Design**: Clean separation of data processing, modeling, training, and prediction
- **MLflow Integration**: Comprehensive experiment tracking and model versioning
- **Configurable**: Multiple configuration presets for different use cases
- **Efficient Processing**: Uses existing `preprocessors/process_traffic.py` for raw data processing
- **Lazy Loading**: Lightweight imports avoid loading ML dependencies unnecessarily

## Module Structure

```
sequence/
├── __init__.py          # Package initialization
├── config.py            # Configuration management
├── data_processor.py    # Data loading and preprocessing
├── models.py           # MLX LSTM model definitions
├── trainer.py          # Model training with MLflow tracking
├── predictor.py        # Inference and prediction utilities
├── explainer.py        # SHAP-based model explainability
├── main.py             # Main entry point and CLI
├── requirements.txt    # Dependencies
└── README.md          # This file
```

## Quick Start

### Installation

```bash
pip install -r requirements.txt
```

### Basic Usage

```python
from sequence import NEXCOTrafficSystem, QUICK_TEST_CONFIG

# Initialize system with quick test configuration
system = NEXCOTrafficSystem(QUICK_TEST_CONFIG)

# Run full pipeline: data processing + training
models = system.run_full_pipeline()

# Make predictions
prediction = system.predict_route("route_name", recent_data, recent_features)
```

### Command Line Interface

```bash
# Run full pipeline with default configuration
python -m sequence.main --mode full

# Quick test run
python -m sequence.main --config quick --mode full

# Production training
python -m sequence.main --config production --mode train --epochs 100

# Custom configuration
python -m sequence.main --epochs 50 --batch-size 64 --max-routes 10
```

## Configuration Options

### Presets

- **DEFAULT_CONFIG**: Balanced configuration for typical use
- **QUICK_TEST_CONFIG**: Fast configuration for testing (1 month, 1 route, 10 epochs)
- **PRODUCTION_CONFIG**: Full-scale configuration (12 months, 50 routes, 100 epochs)

### Custom Configuration

```python
from sequence import Config

config = Config()
config.training.epochs = 100
config.training.batch_size = 64
config.data.months_limit = 6
config.prediction.max_routes_training = 20

system = NEXCOTrafficSystem(config)
```

## Data Processing Pipeline

### Directory Structure
```
./data/
├── raw/
│   ├── traffic/202405/01/*.csv  (raw traffic CSV files)
│   ├── traffic/202406/02/*.csv
│   └── events/202405/01/*.csv   (road events CSV files)
└── processed/
    ├── traffic/
    │   ├── traffic_202405.parquet  (clean traffic data)
    │   └── traffic_202406.parquet
    └── events/
        └── events_202405.parquet   (clean event data)
```

### Stage 1: Raw Data Processing (preprocessors/process_traffic.py)
- **Input**: `./data/raw/traffic/YYYYMM/DD/*.csv` files
- **Output**: `./data/processed/traffic/traffic_YYYYMM.parquet` files
- **Features**: Parallel processing, comprehensive error handling, full column mapping (24 columns)

### Stage 2: ML Preprocessing (sequence/data_processor.py)
- **Input**: Processed parquet files from `./data/processed/traffic/`
- **Output**: ML-ready datasets with enhanced features
- **Enhanced Features**:
  - **Temporal**: hour, day_of_week, is_weekend, is_rush_hour, is_holiday
  - **Weather**: has_precipitation, max_precip  
  - **Infrastructure**: speed_profile_numeric

## Model Architecture

### Enhanced MLX LSTM Model
- **Dual-stream architecture**: Separate processing for duration and features
- **Apple Silicon optimized**: Uses MLX framework for NPU/GPU acceleration
- **Multi-layer LSTM**: Combined feature processing with dropout regularization

### Training Features
- **Early stopping**: Prevents overfitting with configurable patience
- **MLflow tracking**: Comprehensive experiment logging
- **Batch processing**: Efficient training on large datasets

## Prediction

### Single Route Prediction
```python
from sequence import TrafficPredictor

predictor = TrafficPredictor(trained_models)
prediction = predictor.predict_traffic(route_name, recent_data, recent_features)
```

### Batch Prediction
```python
predictions = predictor.predict_batch({
    'route1': {'recent_data': data1, 'recent_features': features1},
    'route2': {'recent_data': data2, 'recent_features': features2}
})
```

### Confidence Intervals
```python
confidence_pred = predictor.predict_with_confidence(
    route_name, recent_data, recent_features, num_samples=10
)
```

## SHAP Explainability

### Enable SHAP Explanations
```python
# Configure SHAP
config = Config()
config.shap.enable_shap = True
config.shap.generate_plots = True

system = NEXCOTrafficSystem(config)
```

### Setup and Use
```python
# Train models and setup SHAP
system.train_models(df)
system.setup_shap_explainers()

# Predict with explanation
result = system.predict_with_explanation(
    route_name, recent_data, recent_features,
    generate_plots=True, save_dir="./shap_plots"
)

# Access feature importance
importance = result['feature_importance']
top_features = sorted(importance.items(), key=lambda x: abs(x[1]), reverse=True)[:5]
```

### Command Line SHAP
```bash
# Train with SHAP enabled
python -m sequence.main --mode train --enable-shap --shap-plots

# Use different explainer
python -m sequence.main --enable-shap --shap-explainer permutation
```

For detailed SHAP documentation, see [SHAP_INTEGRATION.md](../SHAP_INTEGRATION.md).

## MLflow Integration

The system automatically tracks:
- Model hyperparameters
- Training metrics (loss, RMSE, MAE)
- Data statistics
- Route information
- Enhanced feature usage

View results:
```bash
mlflow ui
# Open http://localhost:5000
```

## Performance Optimization

### Apple Silicon Features
- **MLX Framework**: Native Apple Silicon acceleration
- **Unified Memory**: Efficient memory usage across CPU/GPU/NPU
- **Metal Performance Shaders**: Optimized linear algebra operations

### Data Processing
- **Polars**: Fast data processing library
- **Parquet Format**: Efficient columnar storage
- **Batch Processing**: Memory-efficient large dataset handling

## Example Usage

### Complete Pipeline Example

```python
from sequence import NEXCOTrafficSystem, Config
import numpy as np

# Create custom configuration
config = Config()
config.data.months_limit = 3
config.prediction.max_routes_training = 5
config.training.epochs = 30

# Initialize and run system
system = NEXCOTrafficSystem(config)
trained_models = system.run_full_pipeline()

# Get system information
info = system.get_system_info()
print(f"Trained models: {info['num_models']}")
print(f"Available routes: {info['available_routes']}")

# Make prediction (example with dummy data)
route_name = list(trained_models.keys())[0]
recent_data = np.random.random(144) * 1000  # 144 time steps
recent_features = np.random.random((144, 8))  # 144 steps, 8 features

prediction = system.predict_route(route_name, recent_data, recent_features)
```

## Troubleshooting

### Common Issues

1. **MLX Not Available**: Ensure you're running on Apple Silicon Mac
2. **Memory Issues**: Reduce batch_size or months_limit in configuration
3. **No Data Found**: Check data paths in configuration
4. **Model Training Fails**: Verify data has sufficient samples per route

### Performance Tips

1. Use **QUICK_TEST_CONFIG** for initial testing
2. Monitor memory usage with larger datasets
3. Use MLflow UI to track training progress
4. Enable parallel processing for data loading

## Dependencies

See `requirements.txt` for complete list. Key dependencies:
- mlx>=0.12.0 (Apple Silicon acceleration)
- pandas>=2.0.0 (Data processing)
- polars>=0.20.0 (Fast data processing)
- mlflow>=2.8.0 (Experiment tracking)
- scikit-learn>=1.3.0 (Preprocessing)

## License

This system is designed for NEXCO traffic analysis and prediction research.