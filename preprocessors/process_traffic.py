#!/usr/bin/env python3
"""
Process restored NEXCO traffic data from traffic_data directory
Combines all CSV files for each month into a single Parquet file with proper datetime parsing
"""

import os
import pandas as pd
import polars as pl
from pathlib import Path
import logging
from concurrent.futures import ThreadPoolExecutor
import glob
import json

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def process_csv_file(csv_path: str) -> pl.DataFrame:
    """Process a single CSV file with proper datetime parsing"""
    try:
        # Read CSV file - first row contains headers
        df_pd = pd.read_csv(
            csv_path, 
            encoding='utf-8',
            on_bad_lines='skip',
            low_memory=False
        )
        
        # Convert to Polars for faster processing
        df_pl = pl.from_pandas(df_pd)
        
        # Add file metadata for tracking
        file_path = Path(csv_path)
        month_folder = file_path.parent.parent.name  # e.g., 202405
        day_folder = file_path.parent.name  # e.g., 01
        file_name = file_path.stem  # e.g., 000402
        
        df_pl = df_pl.with_columns([
            pl.lit(month_folder).alias("source_month"),
            pl.lit(day_folder).alias("source_day"), 
            pl.lit(file_name).alias("source_file")
        ])
        
        return df_pl
        
    except Exception as e:
        logger.error(f"Error processing {csv_path}: {e}")
        return None

def process_month(month_folder: str, base_path: str, output_path: str) -> bool:
    """Process all CSV files for a single month"""
    month_path = os.path.join(base_path, month_folder)
    output_file = os.path.join(output_path, f"traffic_{month_folder}.parquet")
    
    # Skip if output file already exists
    if os.path.exists(output_file):
        file_size = os.path.getsize(output_file) / (1024*1024)  # MB
        logger.info(f"⏭️  Skipping {month_folder}: already processed ({file_size:.1f} MB)")
        return True
    
    logger.info(f"🔄 Processing month: {month_folder}")
    
    # Get all CSV files for this month
    csv_files = []
    for day_folder in sorted(os.listdir(month_path)):
        day_path = os.path.join(month_path, day_folder)
        if os.path.isdir(day_path):
            day_csvs = glob.glob(os.path.join(day_path, "*.csv"))
            csv_files.extend(day_csvs)
    
    logger.info(f"Found {len(csv_files)} CSV files for {month_folder}")
    
    if not csv_files:
        logger.warning(f"No CSV files found for {month_folder}")
        return False
    
    # Process files in parallel
    dataframes = []
    batch_size = 50  # Process in smaller batches to avoid memory issues
    
    for i in range(0, len(csv_files), batch_size):
        batch_files = csv_files[i:i+batch_size]
        logger.info(f"Processing batch {i//batch_size + 1}: files {i+1} to {min(i+batch_size, len(csv_files))}")
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            batch_results = list(executor.map(process_csv_file, batch_files))
        
        # Filter out None results
        valid_dfs = [df for df in batch_results if df is not None]
        dataframes.extend(valid_dfs)
    
    if not dataframes:
        logger.error(f"No valid dataframes for {month_folder}")
        return False
    
    logger.info(f"Combining {len(dataframes)} dataframes for {month_folder}")
    
    # Concatenate all dataframes
    combined_df = pl.concat(dataframes, how="vertical_relaxed")
    
    # Data type conversions and cleaning
    try:
        logger.info("Converting data types...")
        
        # Map the numbered columns to proper names first
        if "3" in combined_df.columns:
            # This means we have numbered columns (0-23), need to rename them
            column_mapping = {
                "0": "region_code",
                "1": "base_code", 
                "2": "office",
                "3": "record_time",
                "4": "route_code_start",
                "5": "route_name_start",
                "6": "direction_code_start",
                "7": "direction_start",
                "8": "unknown1",
                "9": "unknown2", 
                "10": "distance_from_tokyo_start",
                "11": "ic_name_start",
                "12": "route_code_end",
                "13": "route_name_end",
                "14": "direction_code_end",
                "15": "direction_end",
                "16": "unknown3",
                "17": "unknown4",
                "18": "distance_from_tokyo_end", 
                "19": "ic_name_end",
                "20": "segments",
                "21": "duration_seconds",
                "22": "unknown5",
                "23": "unknown6"
            }
            
            # Rename numbered columns to proper names
            for old_col, new_col in column_mapping.items():
                if old_col in combined_df.columns:
                    combined_df = combined_df.rename({old_col: new_col})
        
        # Convert data types and extract ML features
        combined_df = combined_df.with_columns([
            # Convert timestamp with proper error handling
            pl.when(pl.col("record_time").is_not_null())
            .then(pl.col("record_time").str.strptime(pl.Datetime, format="%Y/%m/%d %H:%M:%S", strict=False))
            .otherwise(None)
            .alias("record_time"),
            
            # Convert numeric columns safely
            pl.col("duration_seconds").cast(pl.Float64, strict=False),
            pl.col("segments").cast(pl.Int64, strict=False),
            pl.col("distance_from_tokyo_start").cast(pl.Float64, strict=False),
            pl.col("distance_from_tokyo_end").cast(pl.Float64, strict=False),
        ])
        
        # Extract ML features from base columns
        combined_df = _extract_ml_features(combined_df)
        
    except Exception as e:
        logger.warning(f"Data type conversion issues for {month_folder}: {e}")
    
    # Remove rows with invalid timestamps or missing duration
    initial_rows = combined_df.shape[0]
    combined_df = combined_df.filter(
        (pl.col("record_time").is_not_null()) &
        (pl.col("duration_seconds").is_not_null()) &
        (pl.col("duration_seconds") > 0)
    )
    final_rows = combined_df.shape[0]
    logger.info(f"Filtered data: {initial_rows:,} → {final_rows:,} rows (removed {initial_rows-final_rows:,} invalid records)")
    
    # Sort by record_time
    combined_df = combined_df.sort("record_time")
    
    # Clean up redundant columns (44 → ~31 columns for better ML efficiency)
    columns_to_remove = [
        # Metadata (not needed for ML)
        "source_month", "source_day", "source_file",
        # Redundant temporal features  
        "weekday",           # Duplicate of day_of_week
        "day_of_month",      # Weak predictive power
        "month",             # Only 1 value per file
        # Unknown/dead weight columns
        "unknown1", "unknown2", "unknown3", "unknown4", "unknown5", "unknown6",
        # Redundant weather (keep the processed versions)
        "precip_from", "precip_to",
        # String versions (keep numeric)
        "speed_profile", "connecting_roads_from", "connecting_roads_to",
        # Derived feature (redundant with hour)  
        "time_band"
    ]
    
    # Remove columns that exist in the dataframe
    existing_columns_to_remove = [col for col in columns_to_remove if col in combined_df.columns]
    if existing_columns_to_remove:
        combined_df = combined_df.drop(existing_columns_to_remove)
        logger.info(f"🗑️  Removed {len(existing_columns_to_remove)} redundant columns: {existing_columns_to_remove}")
    
    # Save as Parquet
    combined_df.write_parquet(output_file)
    
    file_size = os.path.getsize(output_file) / (1024*1024)  # MB
    logger.info(f"✅ Saved {month_folder}: {combined_df.shape[0]:,} rows, {combined_df.shape[1]} columns -> {output_file} ({file_size:.1f} MB)")
    
    # Log sample of processed data
    sample_data = combined_df.select(["record_time", "ic_name_start", "ic_name_end", "duration_seconds"]).head(3)
    logger.info(f"Sample data for {month_folder}:\n{sample_data}")
    
    return True

def main():
    """Main processing function"""
    base_path = "/Users/<USER>/dev/nexco-seq/data/raw/traffic"
    output_path = "/Users/<USER>/dev/nexco-seq/data/processed/traffic"
    
    # Create output directory
    os.makedirs(output_path, exist_ok=True)
    
    # Get all month folders (YYYYMM format)
    month_folders = [f for f in os.listdir(base_path) 
                    if len(f) == 6 and f.isdigit() and os.path.isdir(os.path.join(base_path, f))]
    
    month_folders.sort()
    logger.info(f"Found {len(month_folders)} months to process: {month_folders}")
    
    # Process each month
    success_count = 0
    skipped_count = 0
    
    logger.info(f"Processing all {len(month_folders)} months: {month_folders}")
    
    for month_folder in month_folders:
        output_file = os.path.join(output_path, f"traffic_{month_folder}.parquet")
        
        if os.path.exists(output_file):
            skipped_count += 1
            file_size = os.path.getsize(output_file) / (1024*1024)  # MB
            logger.info(f"⏭️  Skipping {month_folder}: already processed ({file_size:.1f} MB)")
            success_count += 1  # Count as success since it's already done
        else:
            if process_month(month_folder, base_path, output_path):
                success_count += 1
    
    logger.info(f"📊 Final Summary:")
    logger.info(f"  ✅ Successfully processed: {success_count}/{len(month_folders)} months")
    logger.info(f"  ⏭️  Previously completed: {skipped_count} months")
    logger.info(f"  🆕 Newly processed: {success_count - skipped_count} months")
    logger.info(f"  📁 Output directory: {output_path}")
    
    # Show final file listing
    parquet_files = sorted([f for f in os.listdir(output_path) if f.endswith('.parquet')])
    if parquet_files:
        total_size = sum(os.path.getsize(os.path.join(output_path, f)) for f in parquet_files) / (1024*1024)
        logger.info(f"  📋 Final output: {len(parquet_files)} parquet files, {total_size:.1f} MB total")
        
        # Show available files
        for pf in parquet_files:
            size = os.path.getsize(os.path.join(output_path, pf)) / (1024*1024)
            logger.info(f"    • {pf} ({size:.1f} MB)")

def _extract_ml_features(df: pl.DataFrame) -> pl.DataFrame:
    """Extract ML features from base traffic data columns."""
    logger.info("🎯 Extracting ML features from base data...")
    
    # 1. Temporal features from record_time
    df = df.with_columns([
        pl.col("record_time").dt.hour().alias("hour"),
        pl.col("record_time").dt.weekday().alias("day_of_week"), 
        pl.col("record_time").dt.day().alias("day_of_month"),
        pl.col("record_time").dt.month().alias("month"),
        pl.col("record_time").dt.strftime("%A").alias("weekday"),
        
        # Weekend flag (0=Monday, 6=Sunday, so 5,6 are weekend)
        (pl.col("record_time").dt.weekday() >= 5).cast(pl.Int8).alias("is_weekend"),
        
        # Rush hour flag (7-9 AM or 5-7 PM)
        ((pl.col("record_time").dt.hour().is_between(7, 9)) | 
         (pl.col("record_time").dt.hour().is_between(17, 19))).cast(pl.Int8).alias("is_rush_hour"),
    ])
    
    # 2. Infrastructure features from route data + constants
    df = _add_infrastructure_features(df)
    
    # 3. Weather features (API integration point)
    df = _add_weather_features(df)
    
    # 4. Holiday features (API integration point)
    df = _add_holiday_features(df)
    
    logger.info("✅ ML feature extraction completed")
    return df


def _add_infrastructure_features(df: pl.DataFrame) -> pl.DataFrame:
    """Add infrastructure features from route names and constants data."""
    logger.info("   🛣️  Adding infrastructure features from constants data")
    
    # Load highway data constants
    constants_path = "/Users/<USER>/dev/nexco-seq/data/constants"
    highway_data = {}
    
    try:
        with open(os.path.join(constants_path, "highway_data.json"), 'r', encoding='utf-8') as f:
            highway_data = json.load(f)
        logger.info(f"   ✅ Loaded highway data for {len(highway_data)} highways")
    except Exception as e:
        logger.warning(f"   ⚠️  Could not load highway_data.json: {e}")
    
    # Map route names to speed profiles
    if highway_data:
        # Create a mapping expression for speed profiles
        route_speed_map = {}
        for highway, data in highway_data.items():
            if "max_speed_kph" in data:
                route_speed_map[highway] = float(data["max_speed_kph"])
        
        # Apply speed profile mapping
        speed_mapping = pl.col("route_name_start").map_elements(
            lambda x: route_speed_map.get(x, 100.0) if x else 100.0,
            return_dtype=pl.Float64
        ).alias("speed_profile_numeric")
        
        df = df.with_columns([speed_mapping])
        logger.info(f"   📊 Mapped {len(route_speed_map)} routes to speed profiles")
    else:
        # Fallback to default
        df = df.with_columns([
            pl.lit(100.0).cast(pl.Float64).alias("speed_profile_numeric")
        ])
    
    return df


def _add_weather_features(df: pl.DataFrame) -> pl.DataFrame:
    """Add weather features by integrating with weather data."""
    logger.info("   🌤️  Integrating weather data from parquet files...")
    
    try:
        logger.info("   🔍 Attempting weather integration import...")
        
        # Import weather integration utilities (try relative first, then absolute)
        try:
            from .weather_integration import WeatherDataLoader, batch_extract_weather_features
        except ImportError:
            # Fallback for direct script execution
            import sys
            import os
            sys.path.append(os.path.dirname(__file__))
            from weather_integration import WeatherDataLoader, batch_extract_weather_features
        logger.info("   ✅ Weather integration modules imported successfully")
        
        # Initialize weather data loader
        logger.info("   🔍 Initializing WeatherDataLoader...")
        weather_loader = WeatherDataLoader()
        logger.info(f"   ✅ WeatherDataLoader initialized with path: {weather_loader.weather_data_path}")
        
        # Check if weather data directory exists
        import os
        if not os.path.exists(weather_loader.weather_data_path):
            raise FileNotFoundError(f"Weather data directory not found: {weather_loader.weather_data_path}")
        
        # List some weather directories to verify data exists
        weather_dirs = [d for d in os.listdir(weather_loader.weather_data_path) 
                       if os.path.isdir(os.path.join(weather_loader.weather_data_path, d)) and not d.startswith('.')]
        logger.info(f"   📁 Found {len(weather_dirs)} weather IC directories (first 3: {weather_dirs[:3]})")
        
        # Extract weather features for all routes
        logger.info("   🔍 Starting batch weather feature extraction...")
        df_with_weather = batch_extract_weather_features(df, weather_loader)
        logger.info(f"   📊 Weather extraction completed. New shape: {df_with_weather.shape}")
        
        # Verify new weather columns exist
        weather_cols = ['temp_start', 'temp_end', 'precip_start', 'precip_end', 'wind_speed_start', 'wind_speed_end']
        missing_cols = [col for col in weather_cols if col not in df_with_weather.columns]
        if missing_cols:
            raise ValueError(f"Missing weather columns after extraction: {missing_cols}")
        
        # Check for variation in weather data (not all identical values)
        for col in weather_cols:
            unique_count = df_with_weather[col].n_unique()
            logger.info(f"   📈 {col}: {unique_count} unique values")
            if unique_count == 1:
                logger.warning(f"   ⚠️  {col} has only 1 unique value - may indicate fallback to defaults")
        
        # Update the existing placeholder weather features
        df_final = df_with_weather.with_columns([
            # Keep existing has_precipitation and max_precip for backward compatibility
            # but calculate them from the new detailed weather features
            ((pl.col("precip_start") > 0.1) | (pl.col("precip_end") > 0.1)).cast(pl.Int8).alias("has_precipitation"),
            pl.max_horizontal([pl.col("precip_start"), pl.col("precip_end")]).alias("max_precip"),
        ])
        
        logger.info("   ✅ Weather features integrated successfully")
        return df_final
        
    except ImportError as e:
        logger.error(f"   ❌ Import error: {e}")
        logger.error(f"   💡 Try running with: python -m preprocessors.process_traffic")
    except FileNotFoundError as e:
        logger.error(f"   ❌ File not found error: {e}")
    except Exception as e:
        logger.error(f"   ❌ Weather integration failed: {type(e).__name__}: {e}")
        import traceback
        logger.error(f"   🔍 Full traceback:\n{traceback.format_exc()}")
        # Fallback to original placeholder implementation
        df = df.with_columns([
            pl.lit(0).cast(pl.Int8).alias("has_precipitation"),
            pl.lit(0.0).cast(pl.Float64).alias("max_precip"),
            # Add new weather features with default values
            pl.lit(15.0).cast(pl.Float64).alias("temp_start"),
            pl.lit(15.0).cast(pl.Float64).alias("temp_end"),
            pl.lit(0.0).cast(pl.Float64).alias("precip_start"),
            pl.lit(0.0).cast(pl.Float64).alias("precip_end"),
            pl.lit(0.0).cast(pl.Float64).alias("wind_speed_start"),
            pl.lit(0.0).cast(pl.Float64).alias("wind_speed_end"),
        ])
        return df


def _add_holiday_features(df: pl.DataFrame) -> pl.DataFrame:
    """Add holiday features. Ready for calendar API integration.""" 
    # TODO: Calendar API integration point
    # Extract unique dates and call calendar API for Japanese holidays
    logger.info("   📅 Calendar API integration ready (using defaults for now)")
    
    df = df.with_columns([
        pl.lit(0).cast(pl.Int8).alias("is_holiday")
    ])
    return df


if __name__ == "__main__":
    main()