import os
import pandas as pd
import polars as pl
from pathlib import Path
import logging
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Tuple
import glob
import numpy as np
from datetime import datetime, timedelta
import math

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_event_column_names():
    """Define column names for road events data (34 columns based on actual CSV)"""
    return [
        # Administrative (4)
        "region_code", "base_code", "office_name", "event_category_code",
        
        # Event Identity (4) 
        "event_category", "event_id", "status_code", "status_name",
        
        # Temporal (2)
        "start_time", "end_time",
        
        # Location Start (6)
        "route_code_start", "route_name_start", "direction_code_start", 
        "direction_start", "distance_start", "ic_name_start",
        
        # Location End (6)
        "route_code_end", "route_name_end", "direction_code_end",
        "direction_end", "distance_end", "ic_name_end", 
        
        # Road Impact (6)
        "unknown1", "detail_1", "lane_type_code", "lane_type",
        "restriction_scope_code", "restriction_scope",
        
        # Event Type (6) - updated based on actual data
        "event_type_code", "event_type", "restriction_type_code", "restriction_type",
        "empty_field", "unknown2", "detail_2"
    ]

def classify_event_type(event_type: str, restriction_type: str) -> str:
    """Classify events into standard categories for impact calculation"""
    if not isinstance(event_type, str) or not isinstance(restriction_type, str):
        return "other"
    
    event_lower = event_type.lower()
    restriction_lower = restriction_type.lower()
    
    # Accident events
    if "事故" in event_type or "accident" in event_lower:
        return "accident"
    
    # Construction events    
    if any(keyword in event_type for keyword in ["工事", "作業", "construction", "work"]):
        if "緊急" in event_type or "emergency" in event_lower:
            return "emergency_work"
        return "construction"
    
    # Road closure events
    if "通行止" in restriction_type or "closure" in restriction_lower:
        return "road_closure"
    
    # Default
    return "other"

def calculate_severity_weight(restriction_scope: str, lane_type: str) -> float:
    """Calculate severity weight based on lanes affected"""
    if not isinstance(restriction_scope, str):
        return 0.5  # Default medium severity
    
    scope_lower = restriction_scope.lower()
    
    # Full road closure
    if "全車線" in restriction_scope or "all" in scope_lower:
        return 1.0
    
    # Multiple lanes
    if any(keyword in restriction_scope for keyword in ["複数", "multiple", "車線"]):
        return 0.6
    
    # Single lane
    if "単一" in restriction_scope or "single" in scope_lower:
        return 0.3
    
    # Default
    return 0.5

def process_events_csv(csv_path: str) -> pl.DataFrame:
    """Process a single events CSV file"""
    try:
        column_names = get_event_column_names()
        
        # Read with pandas first to handle encoding
        df_pd = pd.read_csv(
            csv_path,
            names=column_names,
            encoding='utf-8', 
            on_bad_lines='skip',
            dtype=str,
            low_memory=False
        )
        
        # Convert to Polars
        df_pl = pl.from_pandas(df_pd)
        
        # Add source metadata
        file_path = Path(csv_path)
        month_folder = file_path.parent.parent.name  # 202405 (go up 2 levels)
        day_folder = file_path.parent.name  # 03 (day folder)
        time_file = file_path.stem  # 160802 (time identifier)
        
        df_pl = df_pl.with_columns([
            pl.lit(month_folder).alias("source_month"),
            pl.lit(day_folder).alias("source_day"),
            pl.lit(time_file).alias("source_time"),
            pl.lit(csv_path).alias("source_file")
        ])
        
        return df_pl
        
    except Exception as e:
        logger.error(f"Error processing events file {csv_path}: {e}")
        return None

def process_events_month(month_folder: str, events_base_path: str) -> pl.DataFrame:
    """Process all event files for one month (nested day/file structure)"""
    month_path = os.path.join(events_base_path, month_folder)
    
    if not os.path.exists(month_path):
        logger.warning(f"Events month folder not found: {month_path}")
        return None
    
    # Get all CSV files in nested day folders
    csv_files = []
    for day_folder in sorted(os.listdir(month_path)):
        day_path = os.path.join(month_path, day_folder)
        if os.path.isdir(day_path):
            day_csvs = glob.glob(os.path.join(day_path, "*.csv"))
            csv_files.extend(day_csvs)
    
    if not csv_files:
        logger.warning(f"No CSV files found in {month_path}")
        return None
    
    logger.info(f"Processing {len(csv_files)} event files for {month_folder}")
    
    # Process files in parallel
    with ThreadPoolExecutor(max_workers=8) as executor:
        results = list(executor.map(process_events_csv, csv_files))
    
    # Filter valid dataframes
    valid_dfs = [df for df in results if df is not None]
    
    if not valid_dfs:
        logger.error(f"No valid dataframes for {month_folder}")
        return None
    
    logger.info(f"Successfully processed {len(valid_dfs)}/{len(csv_files)} event files for {month_folder}")
    
    # Combine all files for the month
    combined_df = pl.concat(valid_dfs, how="vertical_relaxed")
    
    return combined_df

def calculate_haversine_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """Calculate haversine distance between two points in kilometers"""
    R = 6371.0  # Earth radius in kilometers
    
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    lat2_rad = math.radians(lat2)
    lon2_rad = math.radians(lon2)
    
    dlat = lat2_rad - lat1_rad
    dlon = lon2_rad - lon1_rad
    
    a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
    distance = R * c
    
    return distance

def calculate_spatial_decay(distance_km: float, max_radius_km: float = 50.0) -> float:
    """Calculate spatial decay factor based on distance from event"""
    if distance_km >= max_radius_km:
        return 0.0
    
    # Exponential decay: e^(-0.1 * distance)
    decay_factor = math.exp(-0.1 * distance_km)
    return decay_factor

def calculate_temporal_decay(time_diff_hours: float, max_hours: float = 24.0) -> float:
    """Calculate temporal decay factor based on time difference from event"""
    abs_time_diff = abs(time_diff_hours)
    
    if abs_time_diff >= max_hours:
        return 0.0
    
    # Exponential decay: e^(-0.1 * time_diff)
    decay_factor = math.exp(-0.1 * abs_time_diff)
    return decay_factor

def calculate_route_event_impact(route_start_ic: str, route_end_ic: str, 
                               prediction_time: datetime, events_df: pl.DataFrame,
                               ic_locations: Dict[str, Tuple[float, float]]) -> float:
    """
    Calculate total event impact for a specific route at prediction time.
    Implements the Event Impact Layer from Stage 2 architecture.
    """
    if events_df.height == 0:
        return 0.0
    
    # Get route coordinates
    route_start_coords = ic_locations.get(route_start_ic)
    route_end_coords = ic_locations.get(route_end_ic)
    
    if not route_start_coords or not route_end_coords:
        logger.warning(f"Missing coordinates for route {route_start_ic} -> {route_end_ic}")
        return 0.0
    
    total_impact = 0.0
    
    # Filter events within time window (24 hours before/after prediction)
    time_window = timedelta(hours=24)
    start_window = prediction_time - time_window
    end_window = prediction_time + time_window
    
    # Convert to polars datetime for filtering
    relevant_events = events_df.filter(
        (pl.col('start_time') >= start_window) & 
        (pl.col('start_time') <= end_window)
    )
    
    # Calculate impact from each relevant event
    for row in relevant_events.iter_rows(named=True):
        event_ic_start = row.get('location_start', '')
        event_ic_end = row.get('location_end', '')
        event_time = row.get('start_time')
        severity_weight = row.get('severity_weight', 0.5)
        
        # Calculate spatial impact (check both event start and end ICs)
        spatial_impacts = []
        
        for event_ic in [event_ic_start, event_ic_end]:
            if event_ic and event_ic in ic_locations:
                event_coords = ic_locations[event_ic]
                
                # Distance to route start and end
                dist_to_start = calculate_haversine_distance(
                    route_start_coords[0], route_start_coords[1],
                    event_coords[0], event_coords[1]
                )
                dist_to_end = calculate_haversine_distance(
                    route_end_coords[0], route_end_coords[1], 
                    event_coords[0], event_coords[1]
                )
                
                # Use minimum distance (closest impact)
                min_distance = min(dist_to_start, dist_to_end)
                spatial_decay = calculate_spatial_decay(min_distance)
                spatial_impacts.append(spatial_decay)
        
        # Use maximum spatial impact
        max_spatial_impact = max(spatial_impacts) if spatial_impacts else 0.0
        
        # Calculate temporal impact
        if event_time:
            time_diff_hours = (prediction_time - event_time).total_seconds() / 3600.0
            temporal_decay = calculate_temporal_decay(time_diff_hours)
        else:
            temporal_decay = 0.0
        
        # Combined event impact
        event_impact = severity_weight * max_spatial_impact * temporal_decay
        total_impact += event_impact
    
    # Cap maximum impact at 5.0 (reasonable upper bound)
    final_impact = min(total_impact, 5.0)
    return final_impact

def calculate_event_impacts(events_df: pl.DataFrame) -> pl.DataFrame:
    """Calculate impact features for events using Event Impact Decay approach"""
    
    # Event-specific severity scores (Japanese events)
    EVENT_SEVERITY_MAP = {
        '事故': 1.0,          # Accident - maximum impact
        '車両火災': 0.9,      # Vehicle fire - very high
        '車両故障': 0.4,      # Vehicle breakdown - moderate
        '工事': 0.7,          # Construction - high impact
        '点検': 0.3,          # Inspection - low-medium
        '清掃': 0.2,          # Cleaning - low
        '補修': 0.5,          # Repair - medium
        '気象': 0.6,          # Weather conditions - medium-high
        '雪氷': 0.8,          # Snow/ice - very high
        '風': 0.4,            # Wind - moderate
        '雨': 0.3,            # Rain - low-medium
        '交通規制': 0.5,      # Traffic regulation - medium
        'その他': 0.3         # Other/Unknown - low-medium
    }
    
    # Lane closure impact multipliers
    LANE_CLOSURE_MAP = {
        '全車線': 3.0,        # All lanes closed - maximum impact
        '一部車線': 1.5,      # Partial lanes closed - medium impact  
        '路肩': 0.5,          # Shoulder lane - minimal impact
        '追越車線': 1.2,      # Passing lane - slight impact
        '走行車線': 1.8,      # Travel lane - high impact
        '（詳細なし）': 1.0   # No details - default
    }
    
    try:
        enhanced_events = events_df.with_columns([
            # Parse timestamps
            pl.col("start_time").str.strptime(pl.Datetime, format="%Y/%m/%d %H:%M:%S", strict=False),
            pl.col("end_time").str.strptime(pl.Datetime, format="%Y/%m/%d %H:%M:%S", strict=False),
            
            # Classify event types
            pl.struct(["event_type", "restriction_type"]).map_elements(
                lambda x: classify_event_type(x["event_type"], x["restriction_type"]),
                return_dtype=pl.String
            ).alias("event_category_clean"),
            
            # Extract route info for matching
            pl.col("route_name_start").alias("affected_route"),
            pl.col("ic_name_start").alias("location_start"),
            pl.col("ic_name_end").alias("location_end")
        ])
        
        # Calculate enhanced severity weights using Japanese event mapping
        def calculate_enhanced_severity(event_type: str, lane_closure: str, regulation_type: str) -> float:
            # Base severity from event type
            base_severity = EVENT_SEVERITY_MAP.get(event_type, 0.3)
            # Lane closure multiplier  
            lane_multiplier = LANE_CLOSURE_MAP.get(lane_closure, 1.0)
            # Additional regulation multiplier for road closures
            regulation_multiplier = 3.0 if '通行止' in str(regulation_type) else 1.0
            
            # Combined severity (capped at 10.0)
            return min(base_severity * lane_multiplier * regulation_multiplier, 10.0)
        
        enhanced_events = enhanced_events.with_columns([
            pl.struct(["event_type", "restriction_scope", "restriction_type"]).map_elements(
                lambda x: calculate_enhanced_severity(
                    x["event_type"] or "その他",
                    x["restriction_scope"] or "（詳細なし）", 
                    x["restriction_type"] or ""
                ),
                return_dtype=pl.Float64
            ).alias("severity_weight")
        ])
        
        logger.info(f"Processed {enhanced_events.shape[0]:,} events with impact calculations")
        
        # Show event type distribution
        event_counts = enhanced_events.group_by("event_category_clean").len().sort("len", descending=True)
        logger.info("Event type distribution:")
        for row in event_counts.iter_rows():
            logger.info(f"  {row[0]}: {row[1]:,} events")
        
        return enhanced_events
        
    except Exception as e:
        logger.error(f"Error calculating event impacts: {e}")
        return events_df

def save_processed_events(events_df: pl.DataFrame, output_path: str, month: str):
    """Save processed events to parquet"""
    output_file = os.path.join(output_path, f"events_{month}.parquet")
    events_df.write_parquet(output_file)
    
    file_size = os.path.getsize(output_file) / (1024*1024)  # MB
    logger.info(f"✅ Saved events {month}: {events_df.shape[0]:,} rows -> {output_file} ({file_size:.1f} MB)")

def main():
    """Main processing function"""
    events_base_path = "/Users/<USER>/dev/nexco-seq/data/raw/events"
    output_path = "/Users/<USER>/dev/nexco-seq/data/processed/events"
    
    # Create output directory
    os.makedirs(output_path, exist_ok=True)
    
    # Get all available month folders from road_events_data directory
    available_months = [f for f in os.listdir(events_base_path) 
                       if len(f) == 6 and f.isdigit() and os.path.isdir(os.path.join(events_base_path, f))]
    available_months.sort()
    
    # Process all available months (not just 2 for testing)
    months_to_process = available_months
    
    logger.info(f"🚨 Processing road events for Event Impact Decay integration")
    logger.info(f"Months to process: {months_to_process}")
    
    for month in months_to_process:
        logger.info(f"\n📅 Processing events for {month}")
        
        # Check if already processed
        output_file = os.path.join(output_path, f"events_{month}.parquet")
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024*1024)
            logger.info(f"⏭️  Skipping {month}: already processed ({file_size:.1f} MB)")
            continue
        
        # Process the month
        events_df = process_events_month(month, events_base_path)
        
        if events_df is None:
            logger.error(f"Failed to process events for {month}")
            continue
        
        # Calculate impact features
        events_with_impacts = calculate_event_impacts(events_df)
        
        # Save processed events
        save_processed_events(events_with_impacts, output_path, month)
    
    # Show summary
    processed_files = glob.glob(os.path.join(output_path, "events_*.parquet"))
    total_size = sum(os.path.getsize(f) for f in processed_files) / (1024*1024)
    
    logger.info(f"\n📊 Processing Complete:")
    logger.info(f"  ✅ Processed: {len(processed_files)} months")
    logger.info(f"  💾 Total size: {total_size:.1f} MB")
    logger.info(f"  📁 Output directory: {output_path}")

if __name__ == "__main__":
    main()