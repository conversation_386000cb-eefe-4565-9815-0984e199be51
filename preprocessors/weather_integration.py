#!/usr/bin/env python3
"""
Weather data integration utilities for NEXCO Traffic Prediction System.
Provides functions to load and integrate weather data with traffic data.
"""

import os
import polars as pl
import pandas as pd
from pathlib import Path
from typing import Dict, Optional, Tuple
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class WeatherDataLoader:
    """Utility class for loading weather data efficiently."""
    
    def __init__(self, weather_data_path: str = "/Users/<USER>/dev/nexco-seq/data/raw/weather"):
        """Initialize weather data loader."""
        self.weather_data_path = weather_data_path
        self._weather_cache = {}  # Cache loaded weather data
        
    def get_weather_file_path(self, ic_name: str, year: int) -> str:
        """Get path to weather parquet file for a specific IC and year."""
        return os.path.join(self.weather_data_path, ic_name, f"{year}.parquet")
    
    def load_weather_for_ic(self, ic_name: str, year: int) -> Optional[pl.DataFrame]:
        """
        Load weather data for a specific IC location and year.
        
        Args:
            ic_name: IC/JCT location name
            year: Year to load data for
            
        Returns:
            Polars DataFrame with weather data or None if not found
        """
        cache_key = f"{ic_name}_{year}"
        
        # Check cache first
        if cache_key in self._weather_cache:
            return self._weather_cache[cache_key]
        
        weather_file = self.get_weather_file_path(ic_name, year)
        
        if not os.path.exists(weather_file):
            logger.warning(f"Weather file not found: {weather_file}")
            return None
            
        try:
            df = pl.read_parquet(weather_file)
            
            # Ensure timestamp is datetime type and sort by timestamp
            df = df.with_columns([
                pl.col("timestamp").cast(pl.Datetime),
            ]).sort("timestamp")
            
            # Cache the loaded data
            self._weather_cache[cache_key] = df
            logger.info(f"Loaded weather data for {ic_name} ({year}): {df.shape[0]} records")
            return df
            
        except Exception as e:
            logger.error(f"Error loading weather file {weather_file}: {e}")
            return None
    
    def get_weather_at_timestamp(self, ic_name: str, timestamp: datetime) -> Dict[str, float]:
        """
        Get weather data for a specific IC at a specific timestamp.
        
        Args:
            ic_name: IC/JCT location name
            timestamp: Timestamp to get weather for
            
        Returns:
            Dictionary with weather features or defaults if not found
        """
        year = timestamp.year
        weather_df = self.load_weather_for_ic(ic_name, year)
        
        # Default weather values if data not available
        default_weather = {
            'temperature_2m': 15.0,  # Default temperature
            'relative_humidity_2m': 60.0,  # Default humidity
            'rain': 0.0,
            'snowfall': 0.0,
            'wind_speed_10m': 0.0,
            'wind_gusts_10m': 0.0,
            'cloud_cover_low': 50.0,
            'is_day': True
        }
        
        if weather_df is None:
            return default_weather
        
        try:
            # Find closest timestamp (within 1 hour)
            target_timestamp = pl.from_pandas(pd.DataFrame({'target': [timestamp]}))['target'][0]
            
            # Filter to records within 1 hour of target
            time_diff = (weather_df['timestamp'] - target_timestamp).abs()
            closest_idx = time_diff.arg_min()
            closest_record = weather_df[closest_idx]
            
            # Check if closest record is within reasonable time (1 hour)
            closest_time_diff = time_diff[closest_idx]
            if closest_time_diff.total_seconds() > 3600:  # More than 1 hour
                logger.warning(f"No weather data within 1 hour for {ic_name} at {timestamp}")
                return default_weather
            
            # Extract weather values
            weather_data = {
                'temperature_2m': float(closest_record['temperature_2m'][0]) if closest_record['temperature_2m'][0] is not None else default_weather['temperature_2m'],
                'relative_humidity_2m': float(closest_record['relative_humidity_2m'][0]) if closest_record['relative_humidity_2m'][0] is not None else default_weather['relative_humidity_2m'],
                'rain': float(closest_record['rain'][0]) if closest_record['rain'][0] is not None else 0.0,
                'snowfall': float(closest_record['snowfall'][0]) if closest_record['snowfall'][0] is not None else 0.0,
                'wind_speed_10m': float(closest_record['wind_speed_10m'][0]) if closest_record['wind_speed_10m'][0] is not None else default_weather['wind_speed_10m'],
                'wind_gusts_10m': float(closest_record['wind_gusts_10m'][0]) if closest_record['wind_gusts_10m'][0] is not None else default_weather['wind_gusts_10m'],
                'cloud_cover_low': float(closest_record['cloud_cover_low'][0]) if closest_record['cloud_cover_low'][0] is not None else default_weather['cloud_cover_low'],
                'is_day': bool(closest_record['is_day'][0]) if closest_record['is_day'][0] is not None else default_weather['is_day']
            }
            
            return weather_data
            
        except Exception as e:
            logger.error(f"Error getting weather at timestamp for {ic_name}: {e}")
            return default_weather


def extract_weather_features_for_route(ic_name_start: str, ic_name_end: str, 
                                     timestamp: datetime, 
                                     weather_loader: WeatherDataLoader) -> Dict[str, float]:
    """
    Extract weather features for a route (start and end IC locations).
    
    Args:
        ic_name_start: Starting IC location name
        ic_name_end: Ending IC location name  
        timestamp: Timestamp for weather lookup
        weather_loader: WeatherDataLoader instance
        
    Returns:
        Dictionary with 6 weather features for the route
    """
    # Get weather data for start and end locations
    weather_start = weather_loader.get_weather_at_timestamp(ic_name_start, timestamp)
    weather_end = weather_loader.get_weather_at_timestamp(ic_name_end, timestamp)
    
    # Extract 6 weather features as planned in PROGRESS.md
    weather_features = {
        'temp_start': weather_start['temperature_2m'],
        'temp_end': weather_end['temperature_2m'],
        'precip_start': weather_start['rain'] + weather_start['snowfall'],  # Total precipitation
        'precip_end': weather_end['rain'] + weather_end['snowfall'],        # Total precipitation
        'wind_speed_start': weather_start['wind_speed_10m'],
        'wind_speed_end': weather_end['wind_speed_10m']
    }
    
    return weather_features


def batch_extract_weather_features(traffic_df: pl.DataFrame, 
                                 weather_loader: WeatherDataLoader) -> pl.DataFrame:
    """
    Extract weather features using vectorized joins (100x faster than row-by-row).
    
    Args:
        traffic_df: Traffic DataFrame with ic_name_start, ic_name_end, record_time
        weather_loader: WeatherDataLoader instance
        
    Returns:
        DataFrame with added weather features
    """
    logger.info(f"🌤️  Extracting weather features for {traffic_df.shape[0]:,} traffic records (vectorized)...")
    
    try:
        # Step 1: Find unique ICs and year for this batch
        unique_ics_start = set(traffic_df['ic_name_start'].unique().to_list())
        unique_ics_end = set(traffic_df['ic_name_end'].unique().to_list())
        all_unique_ics = unique_ics_start.union(unique_ics_end)
        all_unique_ics = [ic for ic in all_unique_ics if ic is not None]
        
        # Get the year from traffic data
        year = traffic_df['record_time'].dt.year()[0]  # Assuming all records are from same year
        
        logger.info(f"   📍 Found {len(all_unique_ics)} unique IC locations for year {year}")
        
        # Step 2: Pre-load ALL weather data for these ICs (bulk loading)
        weather_data_combined = []
        successful_loads = 0
        
        for ic in all_unique_ics:
            weather_df = weather_loader.load_weather_for_ic(ic, year)
            if weather_df is not None:
                # Add IC name column and prepare for joining
                weather_df = weather_df.with_columns([
                    pl.lit(ic).alias('ic_name'),
                    # Round timestamp to hour for better matching
                    pl.col('timestamp').dt.truncate('1h').alias('timestamp_hour')
                ])
                weather_data_combined.append(weather_df)
                successful_loads += 1
            else:
                logger.warning(f"   ⚠️  No weather data for IC: {ic}")
        
        if not weather_data_combined:
            logger.error(f"   ❌ No weather data loaded for any IC")
            return _add_default_weather_features(traffic_df)
        
        # Step 3: Combine all weather data into single DataFrame
        all_weather_df = pl.concat(weather_data_combined)
        logger.info(f"   📊 Loaded {all_weather_df.shape[0]:,} weather records from {successful_loads}/{len(all_unique_ics)} ICs")
        
        # Step 4: Prepare traffic data for joining (round timestamps to hour)
        traffic_with_hour = traffic_df.with_columns([
            pl.col('record_time').dt.truncate('1h').alias('timestamp_hour')
        ])
        
        # Step 5: Vectorized joins (FAST!)
        logger.info("   🔄 Performing vectorized weather joins...")
        
        # Join weather data for START IC
        df_with_start_weather = traffic_with_hour.join(
            all_weather_df.select([
                'ic_name', 'timestamp_hour', 'temperature_2m', 'rain', 'snowfall', 'wind_speed_10m'
            ]),
            left_on=['ic_name_start', 'timestamp_hour'],
            right_on=['ic_name', 'timestamp_hour'], 
            how='left'
        ).rename({
            'temperature_2m': 'temp_start',
            'rain': 'rain_start', 
            'snowfall': 'snowfall_start',
            'wind_speed_10m': 'wind_speed_start'
        })
        
        # Join weather data for END IC (with explicit column renaming)
        weather_end_renamed = all_weather_df.select([
            'ic_name', 'timestamp_hour', 
            pl.col('temperature_2m').alias('temp_end'),
            pl.col('rain').alias('rain_end'), 
            pl.col('snowfall').alias('snowfall_end'),
            pl.col('wind_speed_10m').alias('wind_speed_end')
        ])
        
        df_with_both_weather = df_with_start_weather.join(
            weather_end_renamed,
            left_on=['ic_name_end', 'timestamp_hour'],
            right_on=['ic_name', 'timestamp_hour'],
            how='left'
        )
        
        # Step 6: Create final weather features
        result_df = df_with_both_weather.with_columns([
            # Fill nulls with defaults
            pl.col('temp_start').fill_null(15.0),
            pl.col('temp_end').fill_null(15.0),
            pl.col('wind_speed_start').fill_null(0.0),
            pl.col('wind_speed_end').fill_null(0.0),
            pl.col('rain_start').fill_null(0.0),
            pl.col('snowfall_start').fill_null(0.0),
            pl.col('rain_end').fill_null(0.0),
            pl.col('snowfall_end').fill_null(0.0),
            
            # Combine rain + snow = total precipitation
            (pl.col('rain_start') + pl.col('snowfall_start')).alias('precip_start'),
            (pl.col('rain_end') + pl.col('snowfall_end')).alias('precip_end')
        ]).drop([
            'timestamp_hour', 'rain_start', 'snowfall_start', 'rain_end', 'snowfall_end'
        ])
        
        logger.info(f"✅ Vectorized weather extraction completed: 6 features added")
        return result_df
        
    except Exception as e:
        logger.error(f"❌ Vectorized weather extraction failed: {e}")
        logger.info("🔄 Falling back to default weather features...")
        return _add_default_weather_features(traffic_df)


def _add_default_weather_features(traffic_df: pl.DataFrame) -> pl.DataFrame:
    """Fallback function to add default weather features."""
    return traffic_df.with_columns([
        pl.lit(15.0).cast(pl.Float64).alias("temp_start"),
        pl.lit(15.0).cast(pl.Float64).alias("temp_end"),
        pl.lit(0.0).cast(pl.Float64).alias("precip_start"),
        pl.lit(0.0).cast(pl.Float64).alias("precip_end"),
        pl.lit(0.0).cast(pl.Float64).alias("wind_speed_start"),
        pl.lit(0.0).cast(pl.Float64).alias("wind_speed_end"),
    ])