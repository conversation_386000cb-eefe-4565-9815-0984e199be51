# SHAP Integration for NEXCO Traffic Prediction

This document describes the SHAP (SHapley Additive exPlanations) integration for the NEXCO traffic prediction system, providing model explainability and interpretability features.

## Overview

SHAP integration enables you to understand:
- **Feature Importance**: Which features most influence traffic predictions
- **Individual Predictions**: Why a specific prediction was made
- **Model Behavior**: How the model responds to different input patterns
- **Feature Interactions**: How features work together to influence predictions

## Quick Start

### 1. Enable SHAP in Configuration

```python
from sequence import Config, NEXCOTrafficSystem

# Create config with SHAP enabled
config = Config()
config.shap.enable_shap = True
config.shap.generate_plots = True
config.shap.plots_directory = "./shap_plots"

# Initialize system
system = NEXCOTrafficSystem(config)
```

### 2. Train Models and Setup SHAP

```python
# Load data and train models
df = system.load_data()
trained_models = system.train_models(df)

# Setup SHAP explainers
system.setup_shap_explainers()
```

### 3. Make Predictions with Explanations

```python
# Make prediction with SHAP explanation
result = system.predict_with_explanation(
    route_name="Route A to Route B",
    recent_data=recent_duration_data,
    recent_features=recent_feature_data,
    generate_plots=True
)

# Access results
prediction = result['prediction']
feature_importance = result['feature_importance']
shap_values = result['shap_values']
```

## Command Line Usage

### Basic Training with SHAP

```bash
python -m sequence.main --mode train --enable-shap --shap-plots
```

### Full Pipeline with SHAP

```bash
python -m sequence.main --mode full --enable-shap --shap-explainer kernel --shap-plots-dir ./my_shap_plots
```

### Available SHAP Arguments

- `--enable-shap`: Enable SHAP explanations
- `--shap-explainer`: Choose explainer type (`kernel` or `permutation`)
- `--shap-plots`: Generate visualization plots
- `--shap-plots-dir`: Directory to save plots (default: `./shap_plots`)

## Configuration Options

### SHAP Configuration Class

```python
@dataclass
class SHAPConfig:
    enable_shap: bool = False              # Enable/disable SHAP
    explainer_type: str = "kernel"         # "kernel" or "permutation"
    background_samples: int = 50           # Background data samples
    max_evaluations: int = 100             # Max evaluations for explainer
    generate_plots: bool = True            # Generate visualizations
    save_plots: bool = True                # Save plots to disk
    plots_directory: str = "./shap_plots"  # Plot save directory
    top_features_display: int = 10         # Top features to display
```

## Features and Capabilities

### 1. Feature Importance Analysis

The system analyzes 14 traffic prediction features:

**Temporal Features (5)**:
- `hour`: Hour of day (0-23)
- `day_of_week`: Day of week (0-6)
- `is_weekend`: Weekend flag (0/1)
- `is_rush_hour`: Rush hour flag (0/1)
- `is_holiday`: Holiday flag (0/1)

**Weather Features (8)**:
- `has_precipitation`: Precipitation flag (0/1)
- `max_precip`: Maximum precipitation
- `temp_start`, `temp_end`: Temperature at route endpoints
- `precip_start`, `precip_end`: Precipitation at route endpoints
- `wind_speed_start`, `wind_speed_end`: Wind speed at route endpoints

**Infrastructure Features (1)**:
- `speed_profile_numeric`: Route speed profile

### 2. Visualization Types

#### Feature Importance Plot
- Horizontal bar chart showing feature importance scores
- Color-coded by positive/negative impact
- Configurable number of top features

#### SHAP Summary Plot
- Scatter plot showing feature values vs SHAP values
- Shows feature importance and value distributions
- Aggregated across time steps for time-series data

#### SHAP Waterfall Plot
- Shows how each feature contributes to a single prediction
- Displays the path from baseline to final prediction
- Useful for understanding individual predictions

### 3. Model Compatibility

#### Supported Models
- `MLXLSTMModel`: Dual-stream LSTM with duration and feature inputs
- `Stage2HierarchicalModel`: Enhanced model with event impact layer

#### MLX Integration
- Custom wrapper (`MLXModelWrapper`) for MLX model compatibility
- Handles dual-stream architecture automatically
- Converts between MLX arrays and NumPy arrays seamlessly

## Architecture Details

### SHAP Explainer Components

```python
# Model wrapper for MLX compatibility
wrapper = MLXModelWrapper(
    model=trained_model,
    scaler_duration=duration_scaler,
    scaler_features=feature_scaler,
    input_steps=144
)

# SHAP explainer
explainer = TrafficSHAPExplainer(wrapper, feature_names)

# Analysis report generator
report = SHAPAnalysisReport(explainer)
```

### Data Flow

1. **Background Data Preparation**: Sample training data for SHAP baseline
2. **Model Wrapping**: Wrap MLX model for SHAP compatibility
3. **Explainer Fitting**: Fit SHAP explainer with background data
4. **Explanation Generation**: Generate SHAP values for predictions
5. **Visualization**: Create plots and analysis reports

## Example Usage

### Complete Example

```python
from sequence import NEXCOTrafficSystem, Config

# Setup
config = Config()
config.shap.enable_shap = True
system = NEXCOTrafficSystem(config)

# Train and setup
df = system.load_data()
system.train_models(df)
system.setup_shap_explainers()

# Predict with explanation
result = system.predict_with_explanation(
    "Tokyo to Osaka",
    recent_duration_data,
    recent_feature_data
)

# Analyze results
if result['shap_available']:
    importance = result['feature_importance']
    top_feature = max(importance.items(), key=lambda x: abs(x[1]))
    print(f"Most important feature: {top_feature[0]} ({top_feature[1]:.4f})")
```

### Running the Example Script

```bash
python examples/shap_example.py
```

## Performance Considerations

### Computational Cost
- SHAP explanations add computational overhead
- Kernel explainer: ~100 model evaluations per explanation
- Permutation explainer: Faster but potentially less accurate

### Memory Usage
- Background data cached in memory
- SHAP values stored for visualization
- Consider reducing `background_samples` for large datasets

### Optimization Tips
- Use `permutation` explainer for faster results
- Reduce `max_evaluations` for quicker explanations
- Limit `background_samples` for memory efficiency

## Troubleshooting

### Common Issues

**"SHAP requires enhanced model with 3 components"**
- Ensure model was trained with feature data
- Check model format: (model, duration_scaler, feature_scaler)

**"Insufficient data for SHAP background"**
- Increase training data size
- Reduce `background_samples` requirement

**"Failed to generate SHAP explanation"**
- Check input data shapes match training data
- Verify model compatibility with wrapper

### Debug Mode

Enable verbose logging for SHAP operations:

```python
config.system.verbose = True
config.system.log_level = "DEBUG"
```

## Future Enhancements

- **Temporal SHAP**: Time-step specific explanations
- **Interactive Plots**: Web-based visualization dashboard
- **Feature Interactions**: SHAP interaction values
- **Batch Explanations**: Efficient batch processing
- **Custom Explainers**: Domain-specific explanation methods
