#!/usr/bin/env python3
"""
Extract all unique IC (Interchange) locations from NEXCO traffic data.
Generates a CSV file with route codes, names, and coordinate placeholders.
"""

import os
import polars as pl
import pandas as pd
from pathlib import Path
import logging
import json
from typing import Set, Dict, List

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def extract_unique_locations_data(data_path: str) -> List[Dict]:
    """Extract unique IC/JCT locations from ic_name_start and ic_name_end columns."""
    logger.info("📊 Extracting unique IC/JCT locations...")
    
    location_data = []
    parquet_files = [f for f in os.listdir(data_path) if f.endswith('.parquet')]
    
    if not parquet_files:
        logger.error(f"❌ No parquet files found in {data_path}")
        return location_data
    
    logger.info(f"📁 Found {len(parquet_files)} parquet files")
    
    # Process all parquet files to get comprehensive unique IC names
    all_ic_names = set()
    
    for parquet_file in parquet_files:
        file_path = os.path.join(data_path, parquet_file)
        logger.info(f"🔍 Processing {parquet_file} for IC/JCT data...")
        
        try:
            df = pl.read_parquet(file_path)
            
            # Get unique IC names from start column
            start_ics = df.select("ic_name_start").unique().to_pandas()
            # Get unique IC names from end column  
            end_ics = df.select("ic_name_end").unique().to_pandas()
            
            # Add to set (automatically handles duplicates)
            start_names = start_ics['ic_name_start'].dropna().tolist()
            end_names = end_ics['ic_name_end'].dropna().tolist()
            
            all_ic_names.update(start_names)
            all_ic_names.update(end_names)
            
            logger.info(f"   ✅ Found {len(start_names)} start ICs and {len(end_names)} end ICs in {parquet_file}")
            
        except Exception as e:
            logger.error(f"   ❌ Error processing {parquet_file}: {e}")
            continue
    
    # Remove empty/null entries and create location data
    for ic_name in all_ic_names:
        if ic_name and str(ic_name).strip():
            location_data.append({
                'ic_name': str(ic_name).strip(),
                'lat': None,  # Will be filled by geocoding script
                'lng': None   # Will be filled by geocoding script
            })
    
    # Sort by name for easier management
    location_data = sorted(location_data, key=lambda x: x['ic_name'])
    
    logger.info(f"🎯 Total unique IC/JCT locations: {len(location_data)}")
    return location_data


def save_json_template(location_data: List[Dict], output_file: str) -> None:
    """Save location data as JSON file."""
    logger.info("📝 Saving location data as JSON...")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(location_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"✅ Location JSON saved to: {output_file}")


def main():
    """Main function to extract unique locations and create JSON template."""
    logger.info("🚀 Starting unique location extraction...")
    
    # Paths
    processed_data_path = "/Users/<USER>/dev/nexco-seq/data/processed/traffic"
    output_dir = "/Users/<USER>/dev/nexco-seq/data/constants"
    output_file = os.path.join(output_dir, "ic_locations.json")
    
    # Create output directory if needed
    os.makedirs(output_dir, exist_ok=True)
    
    # Extract unique location data
    location_data = extract_unique_locations_data(processed_data_path)
    
    if not location_data:
        logger.error("❌ No location data found. Exiting.")
        return
    
    # Save JSON template
    save_json_template(location_data, output_file)
    
    # Print summary
    print("\n" + "="*60)
    print("📍 UNIQUE IC/JCT EXTRACTION SUMMARY")
    print("="*60)
    print(f"Total unique IC/JCT locations found: {len(location_data)}")
    
    print(f"\nSample IC/JCT locations:")
    for i, location in enumerate(location_data[:5], 1):
        print(f"  {i}. {location['ic_name']}")
    
    print(f"\n📁 File created: {output_file}")
    print(f"📊 JSON structure: ic_name, lat (null), lng (null)")
    
    print(f"\n🎯 Next steps:")
    print(f"  1. Run geocoding script to fill in coordinates")
    print(f"  2. Use geocoded data for weather API calls")
    print("="*60)


if __name__ == "__main__":
    main()