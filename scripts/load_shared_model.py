#!/usr/bin/env python3
"""
Example script showing how to load and use a shared NEXCO traffic model.

Usage:
    python scripts/load_shared_model.py --model-name nexco_traffic_route_name --version 1
"""

import argparse
import mlflow
import numpy as np
import sys
import os

def load_model_example():
    """Example of how to load and use a shared NEXCO model."""
    
    print("🚛 NEXCO Traffic Model Loading Example")
    print("=" * 50)
    
    try:
        # Method 1: Load from Model Registry (recommended for production)
        print("\n📋 Method 1: Load from Model Registry")
        
        # List available models
        print("\n🔍 Available models:")
        client = mlflow.MlflowClient()
        for rm in client.search_registered_models():
            print(f"   • {rm.name}")
            for mv in rm.latest_versions:
                print(f"     - Version {mv.version} (Stage: {mv.current_stage})")
        
        # Load a specific model (example)
        # model_name = "nexco_traffic_your_route_here"  # Replace with actual model name
        # model = mlflow.pyfunc.load_model(f"models:/{model_name}/1")  # Version 1
        
        print("\n📦 Method 2: Load from Run Artifacts")
        # Alternative: Load from specific run
        # run_id = "your_run_id_here"  # Get from MLflow UI
        # model = mlflow.pyfunc.load_model(f"runs:/{run_id}/model")
        
        print("\n💡 Usage Example:")
        print("""
# After loading the model:
import numpy as np

# Prepare input data (144 time steps)
duration_sequence = np.random.random((1, 144, 1))  # Last 144 duration values
feature_sequence = np.random.random((1, 144, 14))  # Last 144 feature vectors

# Create input dict
model_input = {
    'duration': duration_sequence,
    'features': feature_sequence
}

# Make prediction
predictions = model.predict(model_input)
print(f"Next 12 predictions: {predictions}")
""")
        
        print("\n📊 Model Information:")
        print("   • Input: 144 time steps of duration + 14 features")
        print("   • Output: 12 future predictions")
        print("   • Features: weather, temporal, infrastructure data")
        print("   • Hardware: Optimized for Apple Silicon (MLX)")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n💡 Make sure:")
        print("   1. MLflow server is running: mlflow ui")
        print("   2. You have trained models in mlruns/ directory")
        print("   3. Models are properly registered in Model Registry")

def share_model_instructions():
    """Show how to share models with others."""
    
    print("\n📤 How to Share Your Trained Models:")
    print("=" * 50)
    
    print("\n🎯 Method 1: Share mlruns Directory (Simple)")
    print("""
# On your machine (sender):
tar -czf nexco_models.tar.gz mlruns/
# Send nexco_models.tar.gz to recipient

# On recipient machine:
tar -xzf nexco_models.tar.gz
mlflow ui  # Start MLflow UI
# Models will be available in UI and via API
""")
    
    print("\n🏢 Method 2: MLflow Tracking Server (Advanced)")
    print("""
# Set up shared MLflow server
mlflow server --host 0.0.0.0 --port 5000

# Others connect to your server
export MLFLOW_TRACKING_URI=http://your-ip:5000
mlflow ui  # They can see your models
""")
    
    print("\n🐳 Method 3: Docker Deployment (Production)")
    print("""
# Export model for serving
mlflow models build-docker -m models:/nexco_traffic_route/1 -n nexco-model

# Run model server
docker run -p 8080:8080 nexco-model
# API available at http://localhost:8080/invocations
""")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Load and test NEXCO traffic models")
    parser.add_argument("--demo", action="store_true", help="Show loading examples")
    parser.add_argument("--share-help", action="store_true", help="Show sharing instructions")
    
    args = parser.parse_args()
    
    if args.share_help:
        share_model_instructions()
    else:
        load_model_example()
    
    if not args.demo and not args.share_help:
        print("\n🆘 Usage:")
        print("   python scripts/load_shared_model.py --demo")
        print("   python scripts/load_shared_model.py --share-help")