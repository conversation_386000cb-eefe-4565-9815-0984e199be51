#!/usr/bin/env python3
"""
Fetch historical weather data for all IC/JCT locations using Open-Meteo API.
Creates individual parquet files for each location covering specified years.
Supports single years (e.g., 2024.parquet) or year ranges (e.g., 2023-2024.parquet).
For current year, data collection stops at yesterday.

Usage:
    python fetch_weather_data.py           # Default: 2024
    python fetch_weather_data.py 2024      # Single year
    python fetch_weather_data.py 2023 2024 # Multiple years
"""

import json
import os
import time
import requests
import pandas as pd
import polars as pl
import logging
from typing import List, Optional
from datetime import datetime, date
import urllib.parse

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class WeatherDataFetcher:
    def __init__(self, base_url: str = "https://archive-api.open-meteo.com/v1/archive"):
        """Initialize the weather data fetcher."""
        self.base_url = base_url
        self.request_count = 0
        self.success_count = 0
        self.failed_locations = []
        
        # Weather parameters to fetch
        self.weather_params = [
            "temperature_2m",
            "relative_humidity_2m", 
            "rain",
            "snowfall",
            "wind_speed_10m",
            "wind_gusts_10m",
            "cloud_cover_low",
            "is_day"
        ]

    def fetch_weather_for_location(self, ic_name: str, lat: float, lng: float, 
                                 start_date: str, end_date: str, year: int) -> Optional[pd.DataFrame]:
        """
        Fetch weather data for a single IC location.
        
        Args:
            ic_name: Name of the IC/JCT location
            lat: Latitude coordinate
            lng: Longitude coordinate
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format
            year: Year for the data (used in DataFrame)
            
        Returns:
            DataFrame with weather data if successful, None otherwise
        """
        # Construct API URL
        params = {
            'latitude': lat,
            'longitude': lng,
            'start_date': start_date,
            'end_date': end_date,
            'hourly': ','.join(self.weather_params),
            'timezone': 'Asia/Tokyo'
        }
        
        # Build URL with parameters
        url = f"{self.base_url}?" + urllib.parse.urlencode(params)
        
        try:
            logger.info(f"  🌐 Fetching weather data...")
            
            response = requests.get(url, timeout=30)
            self.request_count += 1
            
            if response.status_code == 200:
                data = response.json()
                
                # Parse the response
                if 'hourly' in data and 'time' in data['hourly']:
                    hourly_data = data['hourly']
                    
                    # Create DataFrame
                    df_data = {
                        'ic_name': [ic_name] * len(hourly_data['time']),
                        'timestamp': hourly_data['time']
                    }
                    
                    # Add weather parameters
                    for param in self.weather_params:
                        if param in hourly_data:
                            df_data[param] = hourly_data[param]
                        else:
                            logger.warning(f"    ⚠️  Missing parameter: {param}")
                            df_data[param] = [None] * len(hourly_data['time'])
                    
                    df = pd.DataFrame(df_data)
                    
                    # Convert timestamp to datetime
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    
                    # Convert data types for efficiency
                    float_cols = [col for col in df.columns if col not in ['ic_name', 'timestamp', 'is_day']]
                    for col in float_cols:
                        df[col] = pd.to_numeric(df[col], errors='coerce').astype('float32')
                    
                    if 'is_day' in df.columns:
                        df['is_day'] = df['is_day'].astype(bool)
                    
                    logger.info(f"    ✅ Retrieved {len(df)} hourly records")
                    return df
                    
                else:
                    logger.error(f"    ❌ Invalid response format: missing hourly data")
                    return None
                    
            else:
                logger.error(f"    ❌ API request failed: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"    ❌ Request failed: {e}")
            return None
        except Exception as e:
            logger.error(f"    ❌ Unexpected error: {e}")
            return None

    def save_weather_data(self, df: pd.DataFrame, ic_name: str, output_dir: str, filename: str) -> bool:
        """
        Save weather data to parquet file.
        
        Args:
            df: Weather DataFrame
            ic_name: IC location name
            output_dir: Base output directory
            filename: Filename for the parquet file (e.g., "2024.parquet")
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create IC-specific directory
            ic_dir = os.path.join(output_dir, ic_name)
            os.makedirs(ic_dir, exist_ok=True)
            
            # Save as parquet using polars for efficiency
            output_file = os.path.join(ic_dir, filename)
            
            # Convert to polars and save
            pl_df = pl.from_pandas(df)
            pl_df.write_parquet(output_file)
            
            logger.info(f"    💾 Saved to: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"    ❌ Failed to save data: {e}")
            return False

    def get_date_range_for_year(self, year: int) -> tuple[str, str]:
        """
        Get start and end dates for a given year.
        For current year, end date is yesterday. For past years, full year.
        
        Args:
            year: Year to get date range for
            
        Returns:
            Tuple of (start_date, end_date) in YYYY-MM-DD format
        """
        current_year = date.today().year
        start_date = f"{year}-01-01"
        
        if year == current_year:
            # For current year, use yesterday as end date
            from datetime import timedelta
            yesterday = date.today() - timedelta(days=1)
            end_date = yesterday.strftime("%Y-%m-%d")
        else:
            # For past years, use full year
            end_date = f"{year}-12-31"
            
        return start_date, end_date

    def generate_filename(self, year: int) -> str:
        """
        Generate filename for a single year.
        
        Args:
            year: Year to process
            
        Returns:
            Filename string (e.g., "2024.parquet")
        """
        return f"{year}.parquet"

    def fetch_all_weather_data(self, locations_file: str, output_dir: str, 
                             years: List[int], resume: bool = True) -> None:
        """
        Fetch weather data for all IC locations for specified years.
        
        Args:
            locations_file: Path to ic_locations.json
            output_dir: Output directory for weather data
            years: List of years to fetch data for (e.g., [2024] or [2023, 2024])
            resume: Whether to skip already processed locations
        """
        logger.info("🚀 Starting weather data collection...")
        
        # Load locations
        logger.info(f"📖 Loading locations from: {locations_file}")
        with open(locations_file, 'r', encoding='utf-8') as f:
            locations = json.load(f)
        
        logger.info(f"📊 Total locations to process: {len(locations)}")
        logger.info(f"📅 Years to process: {years}")
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Process each year separately
        for year in years:
            logger.info(f"\n🗓️  === PROCESSING YEAR {year} ===")
            
            filename = self.generate_filename(year)
            start_date, end_date = self.get_date_range_for_year(year)
            
            logger.info(f"📁 Output filename: {filename}")
            logger.info(f"📊 Date range: {start_date} to {end_date}")
            
            year_success_count = 0
            year_failed_locations = []
            
            # Process each location for this year
            for i, location in enumerate(locations, 1):
                ic_name = location['ic_name']
                lat = location['lat']
                lng = location['lng']
                
                logger.info(f"🌍 [{i}/{len(locations)}] Processing: {ic_name} ({year})")
                
                # Check if already processed (resume capability)
                output_file = os.path.join(output_dir, ic_name, filename)
                
                if resume and os.path.exists(output_file):
                    logger.info(f"    ⏭️  Already processed, skipping...")
                    year_success_count += 1
                    self.success_count += 1
                    continue
                
                # Fetch weather data for this year
                df = self.fetch_weather_for_location(ic_name, lat, lng, start_date, end_date, year)
                
                if df is not None:
                    logger.info(f"    ✅ Retrieved {len(df)} hourly records for {year}")
                    
                    # Save data
                    if self.save_weather_data(df, ic_name, output_dir, filename):
                        year_success_count += 1
                        self.success_count += 1
                    else:
                        year_failed_locations.append(f"{ic_name} ({year})")
                        self.failed_locations.append(f"{ic_name} ({year})")
                else:
                    logger.error(f"    ❌ Failed to fetch data for {ic_name} ({year})")
                    year_failed_locations.append(f"{ic_name} ({year})")
                    self.failed_locations.append(f"{ic_name} ({year})")
                
                # Rate limiting between locations
                time.sleep(1.0)  # 1 second between locations
                
                # Progress update every 10 locations
                if i % 10 == 0:
                    logger.info(f"📈 Year {year} progress: {i}/{len(locations)} completed ({year_success_count} successful)")
            
            # Year summary
            logger.info(f"✅ Year {year} completed: {year_success_count}/{len(locations)} successful")
            if year_failed_locations:
                logger.info(f"❌ Year {year} failures: {len(year_failed_locations)} locations failed")
        
        self._print_summary(years)

    def _print_summary(self, years: List[int]) -> None:
        """Print collection summary."""
        print("\n" + "="*60)
        print("🌤️  WEATHER DATA COLLECTION SUMMARY")
        print("="*60)
        print(f"Total API requests made: {self.request_count}")
        print(f"Successfully processed: {self.success_count}")
        print(f"Failed to process: {len(self.failed_locations)}")
        
        if self.failed_locations:
            print(f"\n❌ Failed locations:")
            for i, location in enumerate(self.failed_locations[:10], 1):  # Show first 10
                print(f"  {i}. {location}")
            if len(self.failed_locations) > 10:
                print(f"  ... and {len(self.failed_locations) - 10} more")
        
        print("\n📊 Data stored in individual IC directories:")
        for year in years:
            print(f"   data/raw/weather/<ic_name>/{year}.parquet")
        print("="*60)


def main():
    """Main function to run the weather data collection."""
    import sys
    
    logger.info("🌤️  Starting IC/JCT weather data collection...")
    
    # Configuration
    locations_file = "/Users/<USER>/dev/nexco-seq/data/constants/ic_locations.json"
    output_dir = "/Users/<USER>/dev/nexco-seq/data/raw/weather"
    
    # Parse command line arguments for years (default to 2024)
    if len(sys.argv) > 1:
        try:
            years = [int(year) for year in sys.argv[1:]]
        except ValueError:
            logger.error("❌ Invalid year format. Please provide years as integers (e.g., 2024 2023)")
            return
    else:
        years = [2024,2025]
    
    logger.info(f"🗓️  Processing years: {years}")
    
    # Check if locations file exists
    if not os.path.exists(locations_file):
        logger.error(f"❌ Locations file not found: {locations_file}")
        logger.info("💡 Please run extract_unique_locations.py and geocode_locations.py first")
        return
    
    # Initialize fetcher and start collection
    fetcher = WeatherDataFetcher()
    fetcher.fetch_all_weather_data(
        locations_file=locations_file,
        output_dir=output_dir,
        years=years,
        resume=True  # Skip already processed locations
    )
    
    logger.info("✅ Weather data collection completed!")


if __name__ == "__main__":
    main()