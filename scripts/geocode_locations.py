#!/usr/bin/env python3
"""
Geocode IC/JCT locations using Google Maps API.
Updates the ic_locations.json file with latitude and longitude coordinates.
"""

import json
import os
import time
import logging
from typing import Dict, List, Optional
import googlemaps

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class LocationGeocoder:
    def __init__(self, api_key: str):
        """Initialize the geocoder with Google Maps API key."""
        self.gmaps = googlemaps.Client(key=api_key)
        self.request_count = 0
        self.success_count = 0
        self.failed_locations = []

    def geocode_location(self, ic_name: str) -> Optional[Dict[str, float]]:
        """
        Geocode a single IC/JCT location name to lat/lng coordinates.
        
        Args:
            ic_name: The IC/JCT name to geocode
            
        Returns:
            Dictionary with 'lat' and 'lng' keys if successful, None otherwise
        """
        # Try multiple search strategies for better accuracy
        search_queries = [
            f"{ic_name} Japan",  # Basic with country
            f"{ic_name} 高速道路 Japan",  # With highway keyword
            f"{ic_name} インターチェンジ Japan",  # With interchange keyword
            f"{ic_name} ジャンクション Japan" if "JCT" in ic_name else None,  # With junction keyword
        ]
        
        # Remove None entries
        search_queries = [q for q in search_queries if q is not None]
        
        for query in search_queries:
            try:
                logger.info(f"  🔍 Trying: {query}")
                
                # Make the geocoding request
                geocode_result = self.gmaps.geocode(query)
                self.request_count += 1
                
                if geocode_result:
                    location = geocode_result[0]['geometry']['location']
                    
                    # Verify it's actually in Japan (rough bounds check)
                    lat, lng = location['lat'], location['lng']
                    if 24.0 <= lat <= 46.0 and 123.0 <= lng <= 146.0:  # Japan bounds
                        logger.info(f"    ✅ Found: {lat:.6f}, {lng:.6f}")
                        return {'lat': lat, 'lng': lng}
                    else:
                        logger.warning(f"    ⚠️  Location outside Japan bounds: {lat}, {lng}")
                        continue
                
                # Add delay to respect API limits
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"    ❌ Error geocoding with query '{query}': {e}")
                continue
        
        logger.error(f"  ❌ Failed to geocode: {ic_name}")
        self.failed_locations.append(ic_name)
        return None

    def geocode_locations_file(self, input_file: str, output_file: str = None) -> None:
        """
        Geocode all locations in the JSON file that don't have coordinates.
        
        Args:
            input_file: Path to the ic_locations.json file
            output_file: Optional different output path
        """
        if output_file is None:
            output_file = input_file
            
        logger.info(f"📖 Loading locations from: {input_file}")
        
        # Load existing data
        with open(input_file, 'r', encoding='utf-8') as f:
            locations = json.load(f)
        
        logger.info(f"📊 Total locations: {len(locations)}")
        
        # Count locations that need geocoding
        need_geocoding = [loc for loc in locations if loc.get('lat') is None or loc.get('lng') is None]
        logger.info(f"🎯 Locations needing geocoding: {len(need_geocoding)}")
        
        if not need_geocoding:
            logger.info("✅ All locations already have coordinates!")
            return
        
        logger.info("🚀 Starting geocoding process...")
        
        # Geocode missing coordinates
        for i, location in enumerate(locations):
            if location.get('lat') is None or location.get('lng') is None:
                ic_name = location['ic_name']
                logger.info(f"📍 [{i+1}/{len(locations)}] Geocoding: {ic_name}")
                
                coords = self.geocode_location(ic_name)
                if coords:
                    location['lat'] = coords['lat']
                    location['lng'] = coords['lng']
                    self.success_count += 1
                    
                    # Save progress every 10 successful geocodes
                    if self.success_count % 10 == 0:
                        self._save_progress(locations, output_file)
                
                # Add delay between requests
                time.sleep(0.2)
        
        # Save final results
        self._save_progress(locations, output_file)
        self._print_summary()

    def _save_progress(self, locations: List[Dict], output_file: str) -> None:
        """Save current progress to file."""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(locations, f, ensure_ascii=False, indent=2)
        logger.info(f"💾 Progress saved to: {output_file}")

    def _print_summary(self) -> None:
        """Print geocoding summary."""
        print("\n" + "="*60)
        print("🌍 GEOCODING SUMMARY")
        print("="*60)
        print(f"Total API requests made: {self.request_count}")
        print(f"Successfully geocoded: {self.success_count}")
        print(f"Failed to geocode: {len(self.failed_locations)}")
        
        if self.failed_locations:
            print(f"\n❌ Failed locations:")
            for i, location in enumerate(self.failed_locations[:10], 1):  # Show first 10
                print(f"  {i}. {location}")
            if len(self.failed_locations) > 10:
                print(f"  ... and {len(self.failed_locations) - 10} more")
        
        print("="*60)


def main():
    """Main function to run the geocoding process."""
    logger.info("🚀 Starting IC/JCT location geocoding...")
    
    # Check for API key
    api_key = os.getenv('GOOGLE_MAPS_API_KEY')
    if not api_key:
        print("❌ Google Maps API key not found!")
        print("Please set your API key using:")
        print("export GOOGLE_MAPS_API_KEY='your_api_key_here'")
        print("\nOr pass it as an argument:")
        print("python geocode_locations.py --api-key your_api_key_here")
        return
    
    # File paths
    input_file = "/Users/<USER>/dev/nexco-seq/data/constants/ic_locations.json"
    
    if not os.path.exists(input_file):
        logger.error(f"❌ Input file not found: {input_file}")
        logger.info("💡 Please run extract_unique_locations.py first to generate the locations file.")
        return
    
    # Initialize geocoder and process locations
    geocoder = LocationGeocoder(api_key)
    geocoder.geocode_locations_file(input_file)
    
    logger.info("✅ Geocoding process completed!")


if __name__ == "__main__":
    import sys
    
    # Handle command line API key argument
    if len(sys.argv) > 2 and sys.argv[1] == '--api-key':
        os.environ['GOOGLE_MAPS_API_KEY'] = sys.argv[2]
    
    main()