#!/usr/bin/env python3
"""
Memory monitoring utilities for NEXCO Traffic Prediction System.
Provides real-time memory tracking for training sessions.
"""

import psutil
import mlx.core as mx
import time
import threading
import json
from datetime import datetime
from typing import Dict, List, Optional
import matplotlib.pyplot as plt
import pandas as pd


class MemoryMonitor:
    """Real-time memory monitoring for training sessions."""
    
    def __init__(self, log_interval: int = 5):
        """
        Initialize memory monitor.
        
        Args:
            log_interval: Seconds between memory checks
        """
        self.log_interval = log_interval
        self.monitoring = False
        self.memory_logs: List[Dict] = []
        self.monitor_thread: Optional[threading.Thread] = None
        
    def get_current_memory(self) -> Dict:
        """Get current memory usage snapshot."""
        try:
            # System info
            process = psutil.Process()
            system_memory = psutil.virtual_memory()
            
            # MLX GPU memory
            try:
                gpu_info = mx.metal.get_memory_info()
                gpu_memory = {
                    'gpu_used_mb': gpu_info.get('current', 0) / (1024**2),
                    'gpu_peak_mb': gpu_info.get('peak', 0) / (1024**2),
                    'gpu_cache_mb': gpu_info.get('cache', 0) / (1024**2),
                    'gpu_available': True
                }
            except:
                gpu_memory = {
                    'gpu_used_mb': 0,
                    'gpu_peak_mb': 0, 
                    'gpu_cache_mb': 0,
                    'gpu_available': False
                }
            
            return {
                'timestamp': datetime.now().isoformat(),
                'ram_used_mb': process.memory_info().rss / (1024**2),
                'ram_percent': process.memory_percent(),
                'system_ram_used_gb': (system_memory.total - system_memory.available) / (1024**3),
                'system_ram_total_gb': system_memory.total / (1024**3),
                'system_ram_percent': system_memory.percent,
                'cpu_percent': process.cpu_percent(),
                **gpu_memory
            }
        except Exception as e:
            return {
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def _monitor_loop(self):
        """Background monitoring loop."""
        while self.monitoring:
            memory_snapshot = self.get_current_memory()
            self.memory_logs.append(memory_snapshot)
            time.sleep(self.log_interval)
    
    def start_monitoring(self):
        """Start background memory monitoring."""
        if not self.monitoring:
            self.monitoring = True
            self.memory_logs = []
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            print(f"🧠 Memory monitoring started (interval: {self.log_interval}s)")
    
    def stop_monitoring(self):
        """Stop background memory monitoring."""
        if self.monitoring:
            self.monitoring = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=self.log_interval + 1)
            print(f"✅ Memory monitoring stopped. Collected {len(self.memory_logs)} samples")
    
    def get_memory_summary(self) -> Dict:
        """Get summary statistics of memory usage."""
        if not self.memory_logs:
            return {"error": "No memory data collected"}
        
        # Convert to DataFrame for easy analysis
        df = pd.DataFrame(self.memory_logs)
        
        # Filter out error records
        valid_df = df[~df['timestamp'].isna()]
        
        if len(valid_df) == 0:
            return {"error": "No valid memory data"}
        
        summary = {
            'duration_minutes': len(valid_df) * self.log_interval / 60,
            'samples_collected': len(valid_df),
            'ram_usage': {
                'peak_mb': float(valid_df['ram_used_mb'].max()),
                'average_mb': float(valid_df['ram_used_mb'].mean()),
                'min_mb': float(valid_df['ram_used_mb'].min())
            },
            'system_memory': {
                'total_gb': float(valid_df['system_ram_total_gb'].iloc[0]),
                'peak_percent': float(valid_df['system_ram_percent'].max()),
                'average_percent': float(valid_df['system_ram_percent'].mean())
            }
        }
        
        # Add GPU info if available
        if 'gpu_used_mb' in valid_df.columns and valid_df['gpu_available'].any():
            summary['gpu_memory'] = {
                'peak_mb': float(valid_df['gpu_used_mb'].max()),
                'average_mb': float(valid_df['gpu_used_mb'].mean()),
                'cache_peak_mb': float(valid_df['gpu_cache_mb'].max())
            }
        
        return summary
    
    def save_logs(self, filepath: str):
        """Save memory logs to JSON file."""
        with open(filepath, 'w') as f:
            json.dump(self.memory_logs, f, indent=2)
        print(f"💾 Memory logs saved to {filepath}")
    
    def plot_memory_usage(self, save_path: Optional[str] = None):
        """Create memory usage plots."""
        if not self.memory_logs:
            print("❌ No memory data to plot")
            return
        
        df = pd.DataFrame(self.memory_logs)
        valid_df = df[~df['timestamp'].isna()].copy()
        
        if len(valid_df) == 0:
            print("❌ No valid memory data to plot")
            return
        
        # Convert timestamps
        valid_df['timestamp'] = pd.to_datetime(valid_df['timestamp'])
        valid_df['minutes'] = (valid_df['timestamp'] - valid_df['timestamp'].iloc[0]).dt.total_seconds() / 60
        
        # Create subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Memory Usage During Training', fontsize=16)
        
        # RAM usage
        axes[0,0].plot(valid_df['minutes'], valid_df['ram_used_mb'], 'b-', linewidth=2)
        axes[0,0].set_title('Process RAM Usage')
        axes[0,0].set_xlabel('Minutes')
        axes[0,0].set_ylabel('RAM (MB)')
        axes[0,0].grid(True, alpha=0.3)
        
        # System memory percentage
        axes[0,1].plot(valid_df['minutes'], valid_df['system_ram_percent'], 'g-', linewidth=2)
        axes[0,1].set_title('System RAM Usage')
        axes[0,1].set_xlabel('Minutes')
        axes[0,1].set_ylabel('RAM (%)')
        axes[0,1].grid(True, alpha=0.3)
        
        # GPU memory (if available)
        if 'gpu_used_mb' in valid_df.columns and valid_df['gpu_available'].any():
            axes[1,0].plot(valid_df['minutes'], valid_df['gpu_used_mb'], 'r-', linewidth=2, label='Used')
            axes[1,0].plot(valid_df['minutes'], valid_df['gpu_cache_mb'], 'orange', linewidth=2, label='Cache')
            axes[1,0].set_title('GPU Memory Usage')
            axes[1,0].set_xlabel('Minutes')
            axes[1,0].set_ylabel('GPU Memory (MB)')
            axes[1,0].legend()
            axes[1,0].grid(True, alpha=0.3)
        else:
            axes[1,0].text(0.5, 0.5, 'No GPU Memory Data', ha='center', va='center', transform=axes[1,0].transAxes)
            axes[1,0].set_title('GPU Memory Usage')
        
        # CPU usage
        if 'cpu_percent' in valid_df.columns:
            axes[1,1].plot(valid_df['minutes'], valid_df['cpu_percent'], 'm-', linewidth=2)
            axes[1,1].set_title('CPU Usage')
            axes[1,1].set_xlabel('Minutes')
            axes[1,1].set_ylabel('CPU (%)')
            axes[1,1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 Memory usage plot saved to {save_path}")
        else:
            plt.show()


def estimate_memory_requirements(batch_size: int = 32, sequence_length: int = 144, 
                               features: int = 14, hidden_size: int = 64) -> Dict:
    """
    Estimate memory requirements for training configuration.
    
    Args:
        batch_size: Training batch size
        sequence_length: Input sequence length  
        features: Number of features
        hidden_size: LSTM hidden size
        
    Returns:
        Memory estimates in MB
    """
    
    # MLX uses float32 (4 bytes per parameter)
    bytes_per_param = 4
    
    # Input data memory
    input_duration_mb = batch_size * sequence_length * 1 * bytes_per_param / (1024**2)
    input_features_mb = batch_size * sequence_length * features * bytes_per_param / (1024**2)
    
    # Model parameters (rough estimates)
    # LSTM parameters: input_size * hidden_size * 4 (gates) * 2 (input + hidden weights)
    duration_lstm_params = 1 * (hidden_size // 2) * 4 * 2
    feature_lstm_params = features * (hidden_size // 2) * 4 * 2  
    combined_lstm_params = hidden_size * (hidden_size // 2) * 4 * 2
    fc_params = (hidden_size // 2) * (hidden_size // 4) + (hidden_size // 4) * 12
    
    total_model_params = duration_lstm_params + feature_lstm_params + combined_lstm_params + fc_params
    model_memory_mb = total_model_params * bytes_per_param / (1024**2)
    
    # Gradient memory (same as model parameters)
    gradient_memory_mb = model_memory_mb
    
    # Activation memory (rough estimate)
    activation_memory_mb = batch_size * sequence_length * hidden_size * bytes_per_param / (1024**2)
    
    # Total estimates
    total_training_mb = (input_duration_mb + input_features_mb + 
                        model_memory_mb + gradient_memory_mb + 
                        activation_memory_mb) * 1.5  # 50% overhead
    
    return {
        'input_data_mb': input_duration_mb + input_features_mb,
        'model_parameters_mb': model_memory_mb,
        'gradients_mb': gradient_memory_mb, 
        'activations_mb': activation_memory_mb,
        'estimated_total_mb': total_training_mb,
        'recommended_ram_gb': max(8, total_training_mb / 1024 * 2),  # 2x safety margin
        'configuration': {
            'batch_size': batch_size,
            'sequence_length': sequence_length,
            'features': features,
            'hidden_size': hidden_size
        }
    }


if __name__ == "__main__":
    # Example usage
    print("🧠 NEXCO Memory Monitor - Example Usage")
    print("=" * 50)
    
    # Test current memory
    monitor = MemoryMonitor(log_interval=2)
    current = monitor.get_current_memory()
    
    print("\n📊 Current Memory Status:")
    for key, value in current.items():
        if isinstance(value, float):
            if 'mb' in key.lower():
                print(f"   {key}: {value:.1f} MB")
            elif 'gb' in key.lower():
                print(f"   {key}: {value:.1f} GB") 
            elif 'percent' in key.lower():
                print(f"   {key}: {value:.1f}%")
        else:
            print(f"   {key}: {value}")
    
    print("\n📈 Memory Requirements Estimation:")
    estimates = estimate_memory_requirements(batch_size=32, hidden_size=64)
    
    print(f"   Input Data: {estimates['input_data_mb']:.1f} MB")
    print(f"   Model Parameters: {estimates['model_parameters_mb']:.1f} MB") 
    print(f"   Gradients: {estimates['gradients_mb']:.1f} MB")
    print(f"   Activations: {estimates['activations_mb']:.1f} MB")
    print(f"   Estimated Total: {estimates['estimated_total_mb']:.1f} MB")
    print(f"   Recommended RAM: {estimates['recommended_ram_gb']:.1f} GB")
    
    print("\n💡 Usage in training:")
    print("   monitor = MemoryMonitor(log_interval=5)")
    print("   monitor.start_monitoring()")
    print("   # ... run training ...")  
    print("   monitor.stop_monitoring()")
    print("   summary = monitor.get_memory_summary()")
    print("   monitor.plot_memory_usage('memory_plot.png')")